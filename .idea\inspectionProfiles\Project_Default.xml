<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="Eslint" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="LossyEncoding" enabled="false" level="WARNING" enabled_by_default="false" />
    <inspection_tool class="PyInterpreterInspection" enabled="false" level="WARNING" enabled_by_default="false" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="false" level="WARNING" enabled_by_default="false">
      <option name="ignoredPackages">
        <value>
          <list size="16">
            <item index="0" class="java.lang.String" itemvalue="scipy" />
            <item index="1" class="java.lang.String" itemvalue="pydantic" />
            <item index="2" class="java.lang.String" itemvalue="transformers" />
            <item index="3" class="java.lang.String" itemvalue="pymongo" />
            <item index="4" class="java.lang.String" itemvalue="pyodbc" />
            <item index="5" class="java.lang.String" itemvalue="icllmlib" />
            <item index="6" class="java.lang.String" itemvalue="langdetect" />
            <item index="7" class="java.lang.String" itemvalue="PyYAML" />
            <item index="8" class="java.lang.String" itemvalue="python-dateutil" />
            <item index="9" class="java.lang.String" itemvalue="numpy" />
            <item index="10" class="java.lang.String" itemvalue="requests" />
            <item index="11" class="java.lang.String" itemvalue="fastapi" />
            <item index="12" class="java.lang.String" itemvalue="elasticsearch" />
            <item index="13" class="java.lang.String" itemvalue="pika" />
            <item index="14" class="java.lang.String" itemvalue="uvicorn" />
            <item index="15" class="java.lang.String" itemvalue="sentence-transformers" />
          </list>
        </value>
      </option>
    </inspection_tool>
  </profile>
</component>