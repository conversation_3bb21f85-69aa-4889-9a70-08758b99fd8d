#!/usr/bin/env python3
"""
Configuration Migration Script for Thai Binh Chatbot
Helps migrate from .env to cfg.yml configuration
"""

import os
import yaml
from pathlib import Path


def create_sample_env_file():
    """Create a sample .env file to show what environment variables were used"""
    env_content = """# Thai Binh Chatbot Environment Variables (DEPRECATED)
# These variables are now managed in cfg.yml

# AI API Keys
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# Model Configuration
DEFAULT_MODEL=gpt-4o-mini
AGENT_MODEL=gpt-4o-mini
TEST_MODEL=gpt-4o-mini
OPENAI_EMBEDDING=text-embedding-3-small

# LLM Parameters
TEMPERATURE=0.0

# Vector Store
FAISS_INDEX_DIR=faiss_index

# Chat Settings
CHAT_SESSIONS_DIR=chat_sessions

# Retrieval Settings
DEFAULT_K=3
SIMILARITY_THRESHOLD=0.7

# File Upload
MAX_FILE_SIZE=10485760

# Frontend URLs
API_URL=https://thaibinh-chatbot.onrender.com
CHATBOT_URL=https://thaibinh-chatbot.onrender.com/docs-rag-agent

# Legacy Neo4j (no longer used)
# NEO4J_URI=neo4j://localhost:7687
# NEO4J_USERNAME=neo4j
# NEO4J_PASSWORD=password
"""
    
    with open(".env.sample", "w", encoding="utf-8") as f:
        f.write(env_content)
    print("✅ Created .env.sample file showing deprecated environment variables")


def update_config_from_env():
    """Update cfg.yml with values from environment variables if they exist"""
    config_path = Path("cfg.yml")
    
    if not config_path.exists():
        print("❌ cfg.yml not found. Please ensure it exists in the project root.")
        return
    
    # Load existing config
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f) or {}
    
    # Environment variable mappings
    env_mappings = {
        'OPENAI_API_KEY': ['ai', 'openai_api_key'],
        'ANTHROPIC_API_KEY': ['ai', 'anthropic_api_key'],
        'DEFAULT_MODEL': ['ai', 'default_model'],
        'AGENT_MODEL': ['ai', 'agent_model'],
        'TEST_MODEL': ['ai', 'test_model'],
        'OPENAI_EMBEDDING': ['ai', 'embedding_model'],
        'TEMPERATURE': ['ai', 'temperature'],
        'FAISS_INDEX_DIR': ['vector_store', 'index_dir'],
        'CHAT_SESSIONS_DIR': ['chat', 'sessions_dir'],
        'DEFAULT_K': ['retrieval', 'default_k'],
        'SIMILARITY_THRESHOLD': ['retrieval', 'similarity_threshold'],
        'MAX_FILE_SIZE': ['file_upload', 'max_file_size'],
        'API_URL': ['frontend', 'api_url'],
        'CHATBOT_URL': ['frontend', 'chatbot_url'],
    }
    
    updated = False
    for env_var, config_path_parts in env_mappings.items():
        env_value = os.getenv(env_var)
        if env_value:
            # Navigate to the nested config location
            current_config = config
            for part in config_path_parts[:-1]:
                if part not in current_config:
                    current_config[part] = {}
                current_config = current_config[part]
            
            # Convert value to appropriate type
            if env_var in ['TEMPERATURE']:
                env_value = float(env_value)
            elif env_var in ['DEFAULT_K', 'MAX_FILE_SIZE']:
                env_value = int(env_value)
            elif env_var == 'SIMILARITY_THRESHOLD':
                env_value = float(env_value)
            
            # Set the value
            current_config[config_path_parts[-1]] = env_value
            updated = True
            print(f"✅ Updated {'.'.join(config_path_parts)} = {env_value}")
    
    if updated:
        # Write back to file
        with open(config_path, 'w', encoding='utf-8') as f:
            yaml.dump(config, f, default_flow_style=False, indent=2)
        print("✅ cfg.yml updated with environment variable values")
    else:
        print("ℹ️  No environment variables found to update cfg.yml")


def validate_config():
    """Validate the current cfg.yml configuration"""
    try:
        from src.config.settings import settings
        print("✅ Settings loaded successfully!")
        print(f"📋 Config summary:")
        summary = settings.get_config_summary()
        for key, value in summary.items():
            print(f"  {key}: {value}")
        
        # Validate settings
        settings.validate_settings()
        print("✅ All settings validated successfully!")
        
    except Exception as e:
        print(f"❌ Configuration validation failed: {e}")


def check_migration_status():
    """Check the migration status and what files still use environment variables"""
    print("\n🔍 Checking migration status...")
    
    # Files that should be migrated
    files_to_check = [
        "src/main.py",
        "src/config/settings.py", 
        "src/llm/get_llm.py",
        "chatbot_frontend/src/dashbroad.py"
    ]
    
    for file_path in files_to_check:
        if os.path.exists(file_path):
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            if 'os.getenv' in content or 'load_dotenv' in content:
                print(f"⚠️  {file_path} still contains environment variable usage")
            else:
                print(f"✅ {file_path} migrated to cfg.yml")
        else:
            print(f"❓ {file_path} not found")


def main():
    """Main migration script"""
    print("🚀 Thai Binh Chatbot Configuration Migration")
    print("=" * 50)
    
    # Step 1: Create sample env file
    create_sample_env_file()
    
    # Step 2: Update config from environment variables if they exist
    print("\n📝 Updating cfg.yml from environment variables...")
    update_config_from_env()
    
    # Step 3: Validate configuration
    print("\n🔧 Validating configuration...")
    validate_config()
    
    # Step 4: Check migration status
    check_migration_status()
    
    print("\n✅ Migration script completed!")
    print("\nNext steps:")
    print("1. Update your API keys in cfg.yml")
    print("2. Test the application: python src/main.py")
    print("3. Remove any old .env files once everything works")


if __name__ == "__main__":
    main()
