import axios from 'axios';

const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:8081';

export const DocumentService = {
  getFiles: () => {
    return axios.get(`${API_URL}/files`);
  },

  uploadFile: (formData: FormData) => {
    return axios.post(`${API_URL}/files`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },

  deleteFile: (fileId: string) => {
    return axios.delete(`${API_URL}/files/${fileId}`);
  },

  getFileChunks: (fileId: string) => {
    return axios.get(`${API_URL}/files/${fileId}/chunks`);
  },

  updateChunk: (fileId: string, chunkId: string, newText: string) => {
    return axios.put(`${API_URL}/files/${fileId}/chunks/${chunkId}`, {
      new_text: newText,
    });
  },
}; 