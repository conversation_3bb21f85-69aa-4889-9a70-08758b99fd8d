"""
JSON data loading and chunking utilities
Optimized for Vietnamese content with configurable chunking parameters
"""

from langchain_community.document_loaders import JSONLoader
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_core.documents import Document
import json
from pathlib import Path
from pprint import pprint
from typing import List, Dict, Any, Optional
import logging
from .chunking_config import get_chunking_config

logger = logging.getLogger(__name__)

def metadata_func(record: dict, metadata: dict) -> dict:
    """Extract metadata from JSON record"""
    metadata["source"] = record.get("source")
    metadata["title"] = record.get("title")
    return metadata


def get_chunk_with_json(file_path: str, content_type: str = "json") -> List[Dict[str, Any]]:
    """
    Load and return JSON data with chunking configuration

    Args:
        file_path: Path to JSON file
        content_type: Type of content for chunking configuration (default: "json")

    Returns:
        List of JSON data objects
    """
    try:
        data = json.loads(Path(file_path).read_text(encoding='utf-8'))
        logger.info(f"Successfully loaded JSON data from {file_path}: {len(data)} records")
        return data
    except Exception as e:
        logger.error(f"Error loading JSON file {file_path}: {e}")
        return []


def chunk_json_content(file_path: str, content_type: str = "json",
                      preprocess: bool = True) -> List[str]:
    """
    Load JSON file and chunk the content using configuration from cfg.yml

    Args:
        file_path: Path to JSON file
        content_type: Type of content for chunking configuration (default: "json")
        preprocess: Whether to preprocess Vietnamese text (default: True)

    Returns:
        List of text chunks
    """
    try:
        # Load JSON data
        data = get_chunk_with_json(file_path, content_type)
        if not data:
            return []

        # Get chunking configuration
        chunking_config = get_chunking_config()
        text_splitter = chunking_config.create_text_splitter(content_type)

        all_chunks = []

        for i, record in enumerate(data):
            # Extract page content
            page_content = ""
            if isinstance(record, dict):
                if 'kwargs' in record and 'page_content' in record['kwargs']:
                    page_content = record['kwargs']['page_content']
                elif 'page_content' in record:
                    page_content = record['page_content']
                elif 'content' in record:
                    page_content = record['content']
                elif 'text' in record:
                    page_content = record['text']

            if not page_content or len(page_content) < 100:  # Skip very short content
                continue

            # Preprocess if requested
            if preprocess:
                page_content = chunking_config.preprocess_vietnamese_text(page_content)

            # Chunk the content
            chunks = text_splitter.split_text(page_content)
            all_chunks.extend(chunks)

        logger.info(f"Successfully chunked JSON content from {file_path}: "
                   f"{len(all_chunks)} chunks created from {len(data)} records")
        return all_chunks

    except Exception as e:
        logger.error(f"Error chunking JSON content from {file_path}: {e}")
        return []


def chunk_json_to_documents(file_path: str, content_type: str = "json",
                           preprocess: bool = True) -> List[Document]:
    """
    Load JSON file and create Document objects with chunked content

    Args:
        file_path: Path to JSON file
        content_type: Type of content for chunking configuration (default: "json")
        preprocess: Whether to preprocess Vietnamese text (default: True)

    Returns:
        List of Document objects with chunked content
    """
    try:
        # Load JSON data
        data = get_chunk_with_json(file_path, content_type)
        if not data:
            return []

        # Get chunking configuration
        chunking_config = get_chunking_config()
        text_splitter = chunking_config.create_text_splitter(content_type)

        all_documents = []

        for i, record in enumerate(data):
            # Extract page content and metadata
            page_content = ""
            metadata = {"record_index": i, "source_file": file_path}

            if isinstance(record, dict):
                if 'kwargs' in record:
                    kwargs = record['kwargs']
                    page_content = kwargs.get('page_content', '')
                    if 'metadata' in kwargs:
                        metadata.update(kwargs['metadata'])
                else:
                    page_content = record.get('page_content',
                                            record.get('content',
                                                     record.get('text', '')))
                    # Extract other fields as metadata
                    for key, value in record.items():
                        if key not in ['page_content', 'content', 'text']:
                            metadata[key] = value

            if not page_content or len(page_content) < 100:  # Skip very short content
                continue

            # Preprocess if requested
            if preprocess:
                page_content = chunking_config.preprocess_vietnamese_text(page_content)

            # Create document and chunk it
            doc = Document(page_content=page_content, metadata=metadata)
            chunks = text_splitter.split_documents([doc])
            all_documents.extend(chunks)

        logger.info(f"Successfully created Document objects from {file_path}: "
                   f"{len(all_documents)} documents created from {len(data)} records")
        return all_documents

    except Exception as e:
        logger.error(f"Error creating documents from JSON {file_path}: {e}")
        return []
# Example usage
if __name__ == "__main__":
    # Example file path - update this to your actual file path
    example_file_path = 'Crawl_web_data/doanthanhnien.json'

    # Test the functions
    try:
        # Test basic JSON loading
        data = get_chunk_with_json(example_file_path)
        print(f"Loaded {len(data)} records from JSON")

        # Test chunking
        chunks = chunk_json_content(example_file_path)
        print(f"Created {len(chunks)} chunks")

        # Test document creation
        documents = chunk_json_to_documents(example_file_path)
        print(f"Created {len(documents)} documents")

    except Exception as e:
        print(f"Error in example usage: {e}")