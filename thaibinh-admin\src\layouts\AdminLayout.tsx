import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  Nav,
  NavList,
  NavItem,
  Page,
  PageHeader,
  PageSidebar,
  PageSection,
} from '@patternfly/react-core';
import { FileIcon, ChatIcon } from '@patternfly/react-icons';

interface AdminLayoutProps {
  children: React.ReactNode;
}

const AdminLayout: React.FC<AdminLayoutProps> = ({ children }) => {
  const navigate = useNavigate();
  const location = useLocation();

  const Navigation = (
    <Nav>
      <NavList>
        <NavItem 
          icon={<FileIcon />}
          isActive={location.pathname === '/documents'}
          onClick={() => navigate('/documents')}
        >
          Document Management
        </NavItem>
        <NavItem
          icon={<ChatIcon />}
          isActive={location.pathname === '/chatbot'}
          onClick={() => navigate('/chatbot')}
        >
          Chatbot Management
        </NavItem>
      </NavList>
    </Nav>
  );

  const Header = (
    <PageHeader
      logo="Thai Binh Admin"
      showNavToggle
    />
  );

  const Sidebar = <PageSidebar nav={Navigation} />;

  return (
    <Page header={Header} sidebar={Sidebar}>
      <PageSection>
        {children}
      </PageSection>
    </Page>
  );
};

export default AdminLayout; 