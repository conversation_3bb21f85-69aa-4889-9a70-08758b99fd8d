"""
<PERSON><PERSON><PERSON> to migrate existing FAISS vector store to use Vietnamese embeddings
"""

import asyncio
import os
import sys
import json
from typing import List, Dict, Any

# Add the src directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.config.settings import settings
from src.core.vector_store import VectorStoreManager
from src.core.embeddings import EmbeddingManager
from langchain_core.documents import Document


class VectorStoreMigrator:
    """Handles migration of vector store to use Vietnamese embeddings"""
    
    def __init__(self):
        self.old_embedding_manager = None
        self.new_embedding_manager = EmbeddingManager()
        self.backup_dir = "vector_store_backup"
        
    def backup_current_store(self):
        """Backup current vector store"""
        print("📁 Creating backup of current vector store...")
        
        os.makedirs(self.backup_dir, exist_ok=True)
        
        import shutil
        if os.path.exists(settings.FAISS_INDEX_DIR):
            backup_path = os.path.join(self.backup_dir, "faiss_index_backup")
            if os.path.exists(backup_path):
                shutil.rmtree(backup_path)
            shutil.copytree(settings.FAISS_INDEX_DIR, backup_path)
            print(f"✅ Backup created at: {backup_path}")
            return True
        else:
            print("⚠️ No existing vector store found to backup")
            return False
    
    async def extract_documents_from_store(self) -> List[Document]:
        """Extract documents from existing vector store"""
        print("📄 Extracting documents from existing vector store...")
        
        try:
            # Try to load the existing store with OpenAI embeddings
            from langchain_openai import OpenAIEmbeddings
            from langchain_community.vectorstores import FAISS
            
            # Use OpenAI embeddings to load existing store
            openai_embeddings = OpenAIEmbeddings(
                api_key=settings.OPENAI_API_KEY,
                model=settings.EMBEDDING_MODEL
            )
            
            if not os.path.exists(os.path.join(settings.FAISS_INDEX_DIR, "index.faiss")):
                print("⚠️ No existing FAISS index found")
                return []
            
            vector_store = FAISS.load_local(
                settings.FAISS_INDEX_DIR,
                openai_embeddings,
                allow_dangerous_deserialization=True
            )
            
            # Extract documents
            # Note: FAISS doesn't provide direct access to original texts
            # We'll need to use similarity search with diverse queries to extract content
            
            print("⚠️ FAISS doesn't directly expose original documents")
            print("   You may need to re-add your original documents to the new store")
            
            return []
            
        except Exception as e:
            print(f"❌ Failed to extract documents: {e}")
            return []
    
    async def create_new_store_with_vietnamese_embeddings(self, documents: List[Document]):
        """Create new vector store with Vietnamese embeddings"""
        print("🇻🇳 Creating new vector store with Vietnamese embeddings...")
        
        try:
            # Create new directory for the Vietnamese embedding store
            new_index_dir = settings.FAISS_INDEX_DIR + "_vietnamese"
            os.makedirs(new_index_dir, exist_ok=True)
            
            # Create new vector store manager
            vector_manager = VectorStoreManager(
                embedding_manager=self.new_embedding_manager,
                persist_directory=new_index_dir
            )
            
            if documents:
                await vector_manager.init_store(documents)
                print(f"✅ Created new vector store with {len(documents)} documents")
            else:
                print("⚠️ No documents to migrate - new empty store created")
            
            return vector_manager
            
        except Exception as e:
            print(f"❌ Failed to create new store: {e}")
            return None
    
    async def test_new_store(self, vector_manager: VectorStoreManager):
        """Test the new vector store"""
        print("🧪 Testing new vector store...")
        
        try:
            test_queries = [
                "Thái Bình",
                "chatbot",
                "hỗ trợ người dân"
            ]
            
            for query in test_queries:
                results = await vector_manager.similarity_search(query, k=2)
                print(f"   Query: '{query}' -> {len(results)} results")
            
            print("✅ New vector store test completed")
            return True
            
        except Exception as e:
            print(f"❌ New store test failed: {e}")
            return False
    
    def switch_to_new_store(self):
        """Switch to use the new vector store"""
        print("🔄 Switching to new vector store...")
        
        try:
            old_dir = settings.FAISS_INDEX_DIR
            new_dir = settings.FAISS_INDEX_DIR + "_vietnamese"
            temp_dir = settings.FAISS_INDEX_DIR + "_old"
            
            # Move old store to temp location
            if os.path.exists(old_dir):
                if os.path.exists(temp_dir):
                    import shutil
                    shutil.rmtree(temp_dir)
                os.rename(old_dir, temp_dir)
            
            # Move new store to main location
            if os.path.exists(new_dir):
                os.rename(new_dir, old_dir)
                print("✅ Successfully switched to Vietnamese embedding store")
                print(f"   Old store backed up at: {temp_dir}")
                return True
            else:
                print("❌ New store not found")
                # Restore old store
                if os.path.exists(temp_dir):
                    os.rename(temp_dir, old_dir)
                return False
                
        except Exception as e:
            print(f"❌ Failed to switch stores: {e}")
            return False


async def main():
    """Main migration function"""
    print("🚀 Starting Vector Store Migration to Vietnamese Embeddings\n")
    
    migrator = VectorStoreMigrator()
    
    # Step 1: Backup current store
    has_backup = migrator.backup_current_store()
    
    # Step 2: Try to extract documents (may not work with FAISS)
    documents = await migrator.extract_documents_from_store()
    
    if not documents:
        print("\n⚠️ IMPORTANT NOTICE:")
        print("   FAISS doesn't provide access to original document texts.")
        print("   You'll need to re-add your original documents using the new embeddings.")
        print("   The migration will create an empty store with Vietnamese embeddings ready.")
        
        proceed = input("\n❓ Continue with empty store creation? (y/N): ").lower().strip()
        if proceed != 'y':
            print("❌ Migration cancelled")
            return False
    
    # Step 3: Create new store with Vietnamese embeddings
    new_store = await migrator.create_new_store_with_vietnamese_embeddings(documents)
    if not new_store:
        print("❌ Migration failed - could not create new store")
        return False
    
    # Step 4: Test new store
    test_ok = await migrator.test_new_store(new_store)
    
    # Step 5: Switch to new store
    if test_ok:
        switch_ok = migrator.switch_to_new_store()
        if switch_ok:
            print("\n🎉 Migration completed successfully!")
            print("\n📋 Next steps:")
            print("   1. Start the embedding service: cd embedding_service && python start_service.py")
            print("   2. Re-add your documents to populate the new vector store")
            print("   3. Test the system with some queries")
            return True
    
    print("\n❌ Migration failed or incomplete")
    return False


def print_help():
    """Print usage help"""
    print("Vietnamese Embedding Migration Tool")
    print("=====================================")
    print()
    print("This tool migrates your FAISS vector store to use Vietnamese embeddings")
    print("instead of OpenAI embeddings.")
    print()
    print("Prerequisites:")
    print("  1. Vietnamese Embedding Service running on port 2603")
    print("  2. Original documents available for re-indexing")
    print()
    print("Usage:")
    print("  python migrate_vector_store.py")
    print()


if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] in ['-h', '--help']:
        print_help()
    else:
        asyncio.run(main())
