import json
import logging
from datetime import datetime
from typing import Any, Dict, Optional


def iso_ts() -> str:
    return datetime.utcnow().isoformat() + "Z"


def _safe_json(data: Dict[str, Any]) -> str:
    try:
        return json.dumps(data, ensure_ascii=False)
    except Exception:
        # Fallback minimal serialization
        return json.dumps({k: str(v) for k, v in data.items()}, ensure_ascii=False)


class StructuredLogger:
    """
    Lightweight wrapper over Python logging to emit structured JSON logs.
    Avoids logging raw content; only log metadata/metrics.
    """

    def __init__(self, name: str):
        self.logger = logging.getLogger(name)

    def _log(self, level: int, event: str, **fields: Any) -> None:
        payload = {
            "timestamp": iso_ts(),
            "event": event,
            "level": logging.getLevelName(level),
            **{k: v for k, v in fields.items() if v is not None},
        }
        self.logger.log(level, _safe_json(payload))

    def info(self, event: str, **fields: Any) -> None:
        self._log(logging.INFO, event, **fields)

    def debug(self, event: str, **fields: Any) -> None:
        self._log(logging.DEBUG, event, **fields)

    def warn(self, event: str, **fields: Any) -> None:
        self._log(logging.WARNING, event, **fields)

    def error(self, event: str, **fields: Any) -> None:
        self._log(logging.ERROR, event, **fields)


def get_logger(name: str) -> StructuredLogger:
    return StructuredLogger(name)

