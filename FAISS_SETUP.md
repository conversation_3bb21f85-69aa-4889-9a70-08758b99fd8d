# Thai Binh Chatbot - FAISS Vector Store Setup

## Thay đổi từ Neo4j sang FAISS

Dự án đã được chuyển đổi từ sử dụng Neo4j graph database sang FAISS vector database để đơn giản hóa việc triển khai và quản lý.

## Cài đặt Dependencies

### 1. Cài đặt Python packages

```bash
pip install -r requirements.txt
```

### 2. Cài đặt FAISS

FAISS đã được thêm vào `requirements.txt`:
- `faiss-cpu==1.8.0` - Phiên bản CPU của FAISS
- `langchain-chroma==0.1.4` - Support cho Chroma vector store (fallback)

## Cấu hình

### 1. Environment Variables (.env file)

```env
# AI/LLM Settings
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here  # Optional

# FAISS Settings
FAISS_INDEX_DIR=faiss_index
CHAT_SESSIONS_DIR=chat_sessions

# Model Settings
DEFAULT_MODEL=gpt-4o-mini
TEMPERATURE=0.0

# Retrieval Settings
DEFAULT_K=3
SIMILARITY_THRESHOLD=0.7
```

### 2. Loại bỏ Neo4j settings

Các environment variables sau không còn cần thiết:
- ~~NEO4J_URI~~
- ~~NEO4J_USERNAME~~ 
- ~~NEO4J_PASSWORD~~

## Các thay đổi chính

### 1. Vector Store

- **Trước:** Neo4j Vector Index
- **Sau:** FAISS Vector Store
- **File:** `src/core/faiss_vector_store.py`

### 2. Memory Management

- **Trước:** Neo4jChatMessageHistory
- **Sau:** FileChatMessageHistory (JSON-based)
- **File:** `src/core/memory.py`

### 3. Cấu hình

- **File:** `src/config/settings.py` - Cập nhật để hỗ trợ FAISS

## Sử dụng

### 1. Migration dữ liệu

Nếu bạn có dữ liệu từ Neo4j, sử dụng script migration:

```bash
# Migration với sample data
python migrate_to_faiss.py

# Migration từ JSON file
python migrate_to_faiss.py path/to/your/data.json
```

### 2. Test FAISS setup

```bash
python test_faiss.py
```

### 3. Chạy ứng dụng

```bash
# Chạy backend
cd src
python main.py

# Hoặc với uvicorn
uvicorn main:app --reload
```

## Cấu trúc dữ liệu FAISS

### Document Format

```python
{
    "page_content": "Nội dung văn bản...",
    "metadata": {
        "filename": "ten_file.pdf",
        "page_number": 1,
        "link": "https://example.com/file.pdf",
        "chunk_id": "unique_id"
    }
}
```

### Vector Store Directory

```
faiss_index/
├── index.faiss          # FAISS index file
├── index.pkl           # Metadata file
└── docstore.pkl        # Document store
```

### Chat Sessions

```
chat_sessions/
├── session_1.json
├── session_2.json
└── ...
```

## API Endpoints

Các API endpoints giữ nguyên, chỉ thay đổi backend:

- `POST /api/v1/chat` - Chat với agent
- `POST /api/v1/upload` - Upload documents
- `GET /api/v1/stats` - Thống kê vector store

## Troubleshooting

### 1. FAISS import error

```bash
pip install faiss-cpu
# Hoặc cho GPU
pip install faiss-gpu
```

### 2. Vector store rỗng

```python
# Kiểm tra stats
from src.core.faiss_vector_store import get_faiss_vector_store
store = get_faiss_vector_store()
print(store.get_stats())

# Thêm sample data
python migrate_to_faiss.py
```

### 3. Memory issues

Chat history được lưu trong files JSON. Để clear:

```python
import shutil
shutil.rmtree("chat_sessions")
```

## Performance

### FAISS vs Neo4j

| Metric | Neo4j | FAISS |
|--------|-------|-------|
| Setup | Complex | Simple |
| Dependencies | Neo4j DB | Python packages only |
| Memory | High | Lower |
| Speed | Good | Very Fast |
| Scalability | High | Medium |
| Maintenance | High | Low |

### Optimization

- Sử dụng `faiss-gpu` cho datasets lớn
- Điều chỉnh `DEFAULT_K` và `SIMILARITY_THRESHOLD`
- Batch processing cho multiple documents

## Backup & Recovery

### Backup

```bash
# Backup vector store
cp -r faiss_index faiss_index_backup

# Backup chat sessions  
cp -r chat_sessions chat_sessions_backup
```

### Recovery

```bash
# Restore từ backup
cp -r faiss_index_backup faiss_index
cp -r chat_sessions_backup chat_sessions
```

## Development

### Adding new documents

```python
from src.core.faiss_vector_store import get_faiss_vector_store
from langchain_core.documents import Document

store = get_faiss_vector_store()
docs = [Document(page_content="...", metadata={...})]
store.add_documents(docs)
```

### Custom retrieval

```python
from src.chains.semantic_search_chunk_chain import get_chunk_retriever

retriever = get_chunk_retriever()
results = retriever.invoke("your query")
```
