#!/usr/bin/env python3
"""
Test script for FAISS-based file management APIs
"""

import requests
import json
import sys
import time

# API base URL
BASE_URL = "http://localhost:8000"

def test_api_status():
    """Test if API is running"""
    print("🔍 Testing API status...")
    try:
        response = requests.get(f"{BASE_URL}/")
        if response.status_code == 200:
            print("✅ API is running")
            return True
        else:
            print(f"❌ API returned status {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Cannot connect to API: {e}")
        return False

def test_stats():
    """Test stats endpoint"""
    print("\n📊 Testing /stats endpoint...")
    try:
        response = requests.get(f"{BASE_URL}/stats")
        if response.status_code == 200:
            data = response.json()
            print("✅ Stats endpoint working")
            print(f"   Total documents: {data.get('total_documents', 0)}")
            print(f"   Vector store type: {data.get('vector_store_type', 'Unknown')}")
            return data
        else:
            print(f"❌ Stats returned status {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ Error testing stats: {e}")
        return None

def test_list_files():
    """Test list files endpoint"""
    print("\n📁 Testing /files endpoint...")
    try:
        response = requests.get(f"{BASE_URL}/files")
        if response.status_code == 200:
            data = response.json()
            print("✅ List files endpoint working")
            print(f"   Total files: {data.get('total', 0)}")
            
            files = data.get('files', [])
            for i, file_info in enumerate(files[:3]):  # Show first 3 files
                print(f"   File {i+1}: {file_info.get('filename')} ({file_info.get('chunk_count')} chunks)")
            
            return data
        else:
            print(f"❌ List files returned status {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ Error testing list files: {e}")
        return None

def test_get_file_chunks(file_id):
    """Test get file chunks endpoint"""
    print(f"\n📄 Testing /files/{file_id}/chunks endpoint...")
    try:
        response = requests.get(f"{BASE_URL}/files/{file_id}/chunks")
        if response.status_code == 200:
            data = response.json()
            print("✅ Get file chunks endpoint working")
            print(f"   Total chunks: {data.get('total_chunks', 0)}")
            
            chunks = data.get('chunks', [])
            if chunks:
                first_chunk = chunks[0]
                content_preview = first_chunk.get('content', '')[:100]
                print(f"   First chunk preview: {content_preview}...")
                return data
            else:
                print("   No chunks found")
                return data
        elif response.status_code == 404:
            print(f"ℹ️ File {file_id} not found (expected if no files uploaded)")
            return None
        else:
            print(f"❌ Get file chunks returned status {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ Error testing get file chunks: {e}")
        return None

def test_get_specific_chunk(file_id, chunk_id):
    """Test get specific chunk endpoint"""
    print(f"\n📝 Testing /files/{file_id}/chunks/{chunk_id} endpoint...")
    try:
        response = requests.get(f"{BASE_URL}/files/{file_id}/chunks/{chunk_id}")
        if response.status_code == 200:
            data = response.json()
            print("✅ Get specific chunk endpoint working")
            content_preview = data.get('content', '')[:100]
            print(f"   Chunk content preview: {content_preview}...")
            return data
        elif response.status_code == 404:
            print(f"ℹ️ Chunk {chunk_id} not found (expected if no specific chunk)")
            return None
        else:
            print(f"❌ Get specific chunk returned status {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ Error testing get specific chunk: {e}")
        return None

def test_search():
    """Test search endpoint"""
    print("\n🔍 Testing /search endpoint...")
    try:
        payload = {
            "query": "Đoàn thanh niên",
            "k": 3
        }
        response = requests.post(f"{BASE_URL}/search", json=payload)
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Search endpoint working")
            print(f"   Results found: {data.get('count', 0)}")
            
            results = data.get('results', [])
            if results:
                first_result = results[0]
                content_preview = first_result.get('content', '')[:100]
                print(f"   First result preview: {content_preview}...")
            
            return data
        else:
            print(f"❌ Search returned status {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ Error testing search: {e}")
        return None

def test_update_chunk_warning(file_id, chunk_id):
    """Test update chunk endpoint (with warning)"""
    print(f"\n✏️ Testing PUT /files/{file_id}/chunks/{chunk_id} endpoint...")
    print("⚠️ WARNING: This test will modify data if file/chunk exists!")
    
    # Don't actually run the test unless specifically requested
    print("ℹ️ Skipping actual update test to preserve data")
    print("   To test manually, use:")
    print(f'   curl -X PUT "{BASE_URL}/files/{file_id}/chunks/{chunk_id}" \\')
    print('     -H "Content-Type: application/json" \\')
    print('     -d "Updated chunk content for testing"')
    
    return True

def test_delete_file_warning(file_id):
    """Test delete file endpoint (with warning)"""
    print(f"\n🗑️ Testing DELETE /files/{file_id} endpoint...")
    print("⚠️ WARNING: This test will delete data if file exists!")
    
    # Don't actually run the test unless specifically requested
    print("ℹ️ Skipping actual delete test to preserve data")
    print("   To test manually, use:")
    print(f'   curl -X DELETE "{BASE_URL}/files/{file_id}"')
    
    return True

def run_comprehensive_test():
    """Run comprehensive test of all endpoints"""
    print("🚀 Starting comprehensive FAISS API tests")
    print("=" * 60)
    
    # Test API status
    if not test_api_status():
        return False
    
    # Test stats
    stats_data = test_stats()
    
    # Test list files
    files_data = test_list_files()
    
    # If there are files, test file-specific endpoints
    if files_data and files_data.get('files'):
        first_file = files_data['files'][0]
        file_id = first_file['file_id']
        
        # Test get file chunks
        chunks_data = test_get_file_chunks(file_id)
        
        if chunks_data and chunks_data.get('chunks'):
            first_chunk = chunks_data['chunks'][0]
            chunk_id = first_chunk['chunk_id']
            
            # Test get specific chunk
            test_get_specific_chunk(file_id, chunk_id)
            
            # Test update chunk (warning only)
            test_update_chunk_warning(file_id, chunk_id)
        
        # Test delete file (warning only)
        test_delete_file_warning(file_id)
    else:
        print("\nℹ️ No files found, skipping file-specific tests")
        print("   Upload some files first using /files or /web-content endpoints")
    
    # Test search
    test_search()
    
    return True

def print_api_documentation():
    """Print API documentation"""
    print("\n📚 FAISS-BASED API ENDPOINTS")
    print("=" * 60)
    
    endpoints = [
        ("GET", "/", "Check API status"),
        ("GET", "/stats", "Get vector store statistics"),
        ("GET", "/files", "List all uploaded files"),
        ("GET", "/files/{file_id}/chunks", "Get chunks of a specific file"),
        ("GET", "/files/{file_id}/chunks/{chunk_id}", "Get a specific chunk"),
        ("PUT", "/files/{file_id}/chunks/{chunk_id}", "Update a specific chunk"),
        ("DELETE", "/files/{file_id}", "Delete a specific file"),
        ("POST", "/search", "Search documents in vector store"),
        ("POST", "/files", "Upload and process a file"),
        ("POST", "/web-content", "Process web content"),
        ("POST", "/docs-rag-agent", "Chat with RAG agent"),
    ]
    
    for method, endpoint, description in endpoints:
        print(f"{method:6} {endpoint:35} - {description}")
    
    print(f"\n🌐 API Documentation: {BASE_URL}/docs")
    print(f"📊 Redoc Documentation: {BASE_URL}/redoc")

def print_manual_commands():
    """Print manual test commands"""
    print("\n💻 MANUAL TEST COMMANDS")
    print("=" * 60)
    
    print("\n1. List files:")
    print(f'curl -X GET "{BASE_URL}/files"')
    
    print("\n2. Get file chunks (replace FILE_ID):")
    print(f'curl -X GET "{BASE_URL}/files/FILE_ID/chunks"')
    
    print("\n3. Get specific chunk (replace FILE_ID and CHUNK_ID):")
    print(f'curl -X GET "{BASE_URL}/files/FILE_ID/chunks/CHUNK_ID"')
    
    print("\n4. Update chunk (replace FILE_ID and CHUNK_ID):")
    print(f'curl -X PUT "{BASE_URL}/files/FILE_ID/chunks/CHUNK_ID" \\')
    print('  -H "Content-Type: application/json" \\')
    print('  -d "New chunk content here"')
    
    print("\n5. Delete file (replace FILE_ID):")
    print(f'curl -X DELETE "{BASE_URL}/files/FILE_ID"')
    
    print("\n6. Search documents:")
    print(f'curl -X POST "{BASE_URL}/search" \\')
    print('  -H "Content-Type: application/json" \\')
    print('  -d \'{"query": "your search query", "k": 3}\'')

if __name__ == "__main__":
    if len(sys.argv) > 1:
        if sys.argv[1] == "--docs":
            print_api_documentation()
        elif sys.argv[1] == "--manual":
            print_manual_commands()
        else:
            print("Usage: python test_file_apis.py [--docs|--manual]")
    else:
        success = run_comprehensive_test()
        print_api_documentation()
        print_manual_commands()
        
        if success:
            print("\n✅ All tests completed successfully!")
            sys.exit(0)
        else:
            print("\n❌ Some tests failed.")
            sys.exit(1)
