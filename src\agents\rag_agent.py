"""RAG agent setup with tools and prompt loaded from configuration."""

import logging
from pathlib import Path
from time import time
from typing import List, Dict, Any

from langchain.agents import AgentExecutor, tool
from langchain.agents.format_scratchpad.openai_tools import (
    format_to_openai_tool_messages,
)
from langchain.agents.output_parsers.openai_tools import (
    OpenAIToolsAgentOutputParser,
)
from langchain_core.prompts import ChatPromptTemplate, MessagesPlaceholder
from langchain_core.runnables.history import RunnableWithMessageHistory
from langchain_core.runnables import RunnableLambda

from chains.semantic_search_chunk_chain import get_chunk_retriever
from config.settings import settings
from core.memory import get_memory
from llm.get_llm import get_model_function
from tools.tools import get_customer_service_infor
from utils.structured_logging import get_logger
from utils.token_utils import estimate_tokens, get_model_token_limit
from utils.usage_tracker import add_retrieval, set_context_after_filter
from utils.request_context import get_query_id

logger = get_logger(__name__)


def _load_text_file(path_str: str) -> str:
    """Load text content from a file path, returning empty string on failure."""
    try:
        path = Path(path_str)
        return path.read_text(encoding="utf-8")
    except Exception as exc:  # pragma: no cover - defensive fallback
        return f"""Configuration prompt not found or unreadable at {path_str}.
Please check cfg.yml prompts.agent_system_prompt_path. Error: {exc}"""


print("✅✅call agent step")

@tool
def get_chunk_tool(question: str) -> str:
    """Search for information in the document knowledge base.

    For questions about Đoàn thanh niên, hội sinh viên, this tool should
    be used to retrieve relevant context.
    """
    try:
        t0 = time()
        retriever = get_chunk_retriever()
        if hasattr(retriever, "invoke"):
            result = retriever.invoke(question)
        elif hasattr(retriever, "get_relevant_documents"):
            result = retriever.get_relevant_documents(question)
        else:
            result = []
        duration_ms = int((time() - t0) * 1000)
        # Prepare retrieval metadata without logging raw content
        docs_meta: List[Dict[str, Any]] = []
        total_tokens = 0
        for doc in result or []:
            try:
                content_tokens = estimate_tokens(getattr(doc, "page_content", ""))
                total_tokens += content_tokens
                md = getattr(doc, "metadata", {}) or {}
                docs_meta.append({
                    "doc_id": md.get("chunk_id") or md.get("id") or md.get("filename") or "unknown",
                    "source": md.get("source") or md.get("filename") or md.get("title"),
                    "link": md.get("link"),
                    "tokens": content_tokens,
                })
            except Exception:
                continue
        qid = get_query_id() or ""
        if qid:
            add_retrieval(qid, docs_meta, total_tokens)
        logger.info(
            "retrieval_complete",
            query_id=qid,
            found=len(result or []),
            duration_ms=duration_ms,
            k=settings.DEFAULT_K,
            total_tokens=total_tokens,
        )
        logger.debug("retrieval_details", query_id=qid, documents=docs_meta[:10])

        # Budget-based trimming of context tokens
        try:
            model_limit = get_model_token_limit(settings.AGENT_MODEL)
            prompt_tokens = estimate_tokens(_load_text_file(settings.AGENT_SYSTEM_PROMPT_PATH))
            query_tokens = estimate_tokens(question)
            reserve_output = 512
            allowed_context_tokens = max(0, model_limit - prompt_tokens - query_tokens - reserve_output)
        except Exception:
            allowed_context_tokens = 0

        # Select top docs until budget
        selected_docs_indices = []
        used_tokens = 0
        for idx, dm in enumerate(docs_meta):
            if used_tokens + dm.get("tokens", 0) > allowed_context_tokens:
                break
            selected_docs_indices.append(idx)
            used_tokens += dm.get("tokens", 0)

        if qid and used_tokens:
            set_context_after_filter(qid, used_tokens)
        if allowed_context_tokens and total_tokens > allowed_context_tokens:
            # Log filtering reasons
            logger.debug(
                "context_trimming",
                query_id=qid,
                allowed_tokens=allowed_context_tokens,
                used_tokens=used_tokens,
                filtered_out=len(docs_meta) - len(selected_docs_indices),
            )

        # Format the result for better readability and extract references
        if isinstance(result, list) and result:
            formatted_result = ""
            references: list[dict] = []

            for i, doc in enumerate(result):
                # Respect selection indices if trimming applied
                if selected_docs_indices and i not in selected_docs_indices:
                    continue
                if hasattr(doc, "page_content"):
                    # Do not log raw content in logs by default; only return via tool output
                    formatted_result += f"Document {i + 1}: {doc.page_content}\n"
                    meta = getattr(doc, "metadata", {}) or {}
                    if meta:
                        formatted_result += f"Metadata: {meta}\n\n"
                        filename = meta.get("filename")
                        link = meta.get("link")
                        if filename:
                            ref = {"filename": str(filename), "link": link}
                            if ref not in references:
                                references.append(ref)
                else:
                    formatted_result += f"Document {i + 1}: {str(doc)}\n\n"

            # Append a machine-readable references section for downstream parsing
            try:
                import json as _json

                formatted_result += (
                    "\nREFERENCES_JSON: " + _json.dumps(references, ensure_ascii=False)
                )
            except Exception:
                # If JSON serialization fails, just return the formatted text
                pass

            # Debug: log full retrieved context content when DEBUG enabled
            if settings.DEBUG:
                # 1) Raw concatenated context for eyeballing
                logger.debug(
                    "retrieved_context_full",
                    query_id=qid,
                    context=formatted_result,
                )
                # 2) Structured per-document context for machine parsing
                try:
                    docs_struct = []
                    for i, doc in enumerate(result):
                        if selected_docs_indices and i not in selected_docs_indices:
                            continue
                        content = getattr(doc, "page_content", str(doc))
                        md = getattr(doc, "metadata", {}) or {}
                        docs_struct.append({
                            "index": i,
                            "tokens": estimate_tokens(content),
                            "metadata": md,
                            "content": content,
                        })
                    logger.debug(
                        "retrieved_context_docs",
                        query_id=qid,
                        documents_count=len(docs_struct),
                        documents=docs_struct,
                        total_tokens=sum(d.get("tokens", 0) for d in docs_struct),
                    )
                except Exception:
                    pass

            return formatted_result
        return "Không tìm thấy thông tin phù hợp trong cơ sở dữ liệu."
    except Exception as exc:  # pragma: no cover - defensive fallback
        return f"Xảy ra lỗi khi tìm kiếm: {exc}"

@tool
def get_customer_service() -> str:
    """Return contact information for customer service."""
    return get_customer_service_infor()


agent_tools = [
    get_chunk_tool,
    get_customer_service,
]
def get_memory_for_session(session_id: str):
    """Return memory object for a given session."""
    return get_memory(session_id, persist=True)


agent_prompt = ChatPromptTemplate.from_messages(
    [
        (
            "system",
            _load_text_file(settings.AGENT_SYSTEM_PROMPT_PATH),
        ),
        MessagesPlaceholder(variable_name="chat_history"),
        ("user", "{input}"),
        MessagesPlaceholder(variable_name="agent_scratchpad"),
    ]
)

agent_llm_with_tools = get_model_function().bind_tools(agent_tools)

# Helper to log the final messages being sent to the LLM
from langchain_core.messages import BaseMessage

def _tap_prompt_messages(prompt_value):
    try:
        # Resolve to a list of messages
        if hasattr(prompt_value, "to_messages"):
            msgs = prompt_value.to_messages()
        elif isinstance(prompt_value, list):
            msgs = prompt_value
        else:
            msgs = [prompt_value]
        # Convert to loggable structure
        serializable_msgs = []
        for m in msgs:
            try:
                role = getattr(m, "type", None) or m.__class__.__name__.replace("Message", "").lower()
                content = getattr(m, "content", "")
                serializable_msgs.append({"role": role, "content": content})
            except Exception:
                serializable_msgs.append({"role": "unknown", "content": str(m)})
        qid = get_query_id() or ""
        logger.debug(
            "llm_input_messages",
            query_id=qid,
            messages=serializable_msgs,
        )
    except Exception:
        # Avoid breaking the chain on logging errors
        pass
    return prompt_value

rag_agent = (
    {
        "input": lambda x: x["input"],
        "agent_scratchpad": lambda x: format_to_openai_tool_messages(
            x["intermediate_steps"]
        ),
        "chat_history": lambda x: x["chat_history"],
    }
    | agent_prompt
    | RunnableLambda(_tap_prompt_messages)
    | agent_llm_with_tools
    | OpenAIToolsAgentOutputParser()
)

rag_agent_executor = AgentExecutor(
    agent=rag_agent,
    tools=agent_tools,
    verbose=True,
    return_intermediate_steps=True,
    handle_parsing_errors=True,
)

chat_agent = RunnableWithMessageHistory(
    rag_agent_executor,
    get_memory_for_session,
    input_messages_key="input",
    history_messages_key="chat_history",
)


def get_agent():
    """Expose the chat agent for use by API endpoints."""
    return chat_agent
