[project]
name = "chatbot_api"
version = "0.1"
requires-python = ">=3.8" 
dependencies = [
    "asyncio==3.4.3",
    "fastapi==0.115.6",
    "langchain==0.3.0",
    "langchain-openai==0.2.0",
    "langchain-community==0.3.0",
    "langchainhub==0.1.14",
    "neo4j==5.14.1",
    "numpy==1.26.2",
    "openai==1.57.2",
    "opentelemetry-api==1.22.0",
    "pydantic==2.10.3",
    "uvicorn==0.25.0",
    "six==1.16.0",
    "python-dotenv==1.0.1",            # Explicitly add six
    "unstructured==0.16.11",
    "pytesseract",
    "trafilatura",
    "certifi",
]

[project.optional-dependencies]
dev = ["black", "flake8"]
