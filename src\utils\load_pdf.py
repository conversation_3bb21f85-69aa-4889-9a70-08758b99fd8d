"""
PDF document loading and chunking utilities
Optimized for Vietnamese content with configurable chunking parameters
"""

from langchain_community.document_loaders import PDFPlumberLoader
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_core.documents import Document
from typing import List, Optional
import logging
from .chunking_config import get_chunking_config

logger = logging.getLogger(__name__)

def chunk_text(filename: str, content_type: str = "pdf",
               preprocess: bool = True) -> List[Document]:
    """
    Load and chunk PDF text using configuration from cfg.yml

    Args:
        filename: Path to PDF file
        content_type: Type of content for chunking configuration (default: "pdf")
        preprocess: Whether to preprocess Vietnamese text (default: True)

    Returns:
        List of Document objects with chunked content
    """
    try:
        # Load PDF
        loader = PDFPlumberLoader(filename)
        docs = loader.lazy_load()

        if not docs:
            logger.warning(f"No content loaded from PDF: {filename}")
            return []

        # Get chunking configuration
        chunking_config = get_chunking_config()
        text_splitter = chunking_config.create_text_splitter(content_type)

        # Preprocess documents if requested
        if preprocess:
            processed_docs = []
            for doc in docs:
                processed_content = chunking_config.preprocess_vietnamese_text(doc.page_content)
                processed_doc = Document(
                    page_content=processed_content,
                    metadata=doc.metadata
                )
                processed_docs.append(processed_doc)
            docs = processed_docs

        # Split documents into chunks
        chunks = text_splitter.split_documents(docs)

        logger.info(f"Successfully chunked PDF {filename}: {len(chunks)} chunks created")
        return chunks

    except Exception as e:
        logger.error(f"Error processing PDF {filename}: {str(e)}")
        return []


def chunk_pdf_with_custom_config(filename: str, chunk_size: int,
                                chunk_overlap: int,
                                separators: Optional[List[str]] = None) -> List[Document]:
    """
    Load and chunk PDF with custom configuration

    Args:
        filename: Path to PDF file
        chunk_size: Custom chunk size
        chunk_overlap: Custom chunk overlap
        separators: Custom separators list

    Returns:
        List of Document objects with chunked content
    """
    try:
        loader = PDFPlumberLoader(filename)
        docs = loader.lazy_load()

        if not docs:
            logger.warning(f"No content loaded from PDF: {filename}")
            return []

        # Use custom configuration
        chunking_config = get_chunking_config()
        if separators is None:
            separators = chunking_config.get_vietnamese_separators()

        text_splitter = RecursiveCharacterTextSplitter(
            separators=separators,
            chunk_size=chunk_size,
            chunk_overlap=chunk_overlap,
            length_function=len,
            keep_separator=True,
            strip_whitespace=True,
            add_start_index=True,
        )

        chunks = text_splitter.split_documents(docs)

        logger.info(f"Successfully chunked PDF {filename} with custom config: "
                   f"{len(chunks)} chunks created")
        return chunks

    except Exception as e:
        logger.error(f"Error processing PDF {filename} with custom config: {str(e)}")
        return []

