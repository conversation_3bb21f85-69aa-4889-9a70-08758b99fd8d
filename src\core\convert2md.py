import os
from docx import Document
import pypandoc
import pdfplumber
import re

# Constants for file size limits (in bytes)
MAX_FILE_SIZE = 50 * 1024 * 1024  # 50MB
SUPPORTED_EXTENSIONS = {'.docx', '.pdf'}

class DocumentConverter:
    """
    Document converter utility for converting various document formats to Markdown.

    Supports conversion from DOCX and PDF formats to Markdown with proper
    structure preservation and error handling.
    """

    def __init__(self, output_dir: str = 'output'):
        """
        Initialize the DocumentConverter.

        Args:
            output_dir: Directory to store converted files
        """
        self.output_dir = output_dir
        os.makedirs(output_dir, exist_ok=True)
    
    def _validate_file(self, input_path: str) -> bool:
        """
        Validate input file before processing.

        Args:
            input_path: Path to the input file

        Returns:
            True if file is valid, False otherwise
        """
        if not os.path.exists(input_path):
            print(f"File not found: {input_path}")
            return False

        file_size = os.path.getsize(input_path)
        if file_size > MAX_FILE_SIZE:
            print(f"File too large: {file_size} bytes (max: {MAX_FILE_SIZE} bytes)")
            return False

        file_extension = os.path.splitext(input_path)[1].lower()
        if file_extension not in SUPPORTED_EXTENSIONS:
            print(f"Unsupported file format: {file_extension}")
            return False

        return True

    def docx_to_markdown(self, input_path: str) -> str:
        """
        Convert DOCX file to Markdown format.

        Args:
            input_path: Path to the input DOCX file

        Returns:
            Path to the converted Markdown file, or None if conversion failed
        """
        if not self._validate_file(input_path):
            return None

        try:
            output_path = os.path.join(
                self.output_dir,
                os.path.splitext(os.path.basename(input_path))[0] + '.md'
            )
            pypandoc.convert_file(
                input_path,
                'markdown',
                outputfile=output_path,
                format='docx'
            )
            print(f"Successfully converted: {output_path}")
            return output_path
        except (OSError, RuntimeError, FileNotFoundError) as e:
            print(f"Error converting DOCX file: {str(e)}")
            return None

    def pdf_to_markdown(self, input_path: str) -> str:
        """
        Convert PDF file to Markdown format.

        Args:
            input_path: Path to the input PDF file

        Returns:
            Path to the converted Markdown file, or None if conversion failed
        """
        if not self._validate_file(input_path):
            return None

        try:
            output_path = os.path.join(
                self.output_dir,
                os.path.splitext(os.path.basename(input_path))[0] + '.md'
            )
            markdown_content = []

            with pdfplumber.open(input_path) as pdf:
                for page in pdf.pages:
                    text = page.extract_text()
                    if text:
                        paragraphs = text.split('\n\n')
                        for para in paragraphs:
                            para = para.strip()
                            if not para:
                                continue
                            # Detect potential headers (all caps, numbers, spaces)
                            if re.match(r'^[A-Z\d\s]{10,}$', para) and len(para) < 100:
                                markdown_content.append(f"# {para}\n\n")
                            else:
                                markdown_content.append(f"{para}\n\n")

            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(''.join(markdown_content))

            print(f"Successfully converted: {output_path}")
            return output_path
        except (OSError, FileNotFoundError, IOError) as e:
            print(f"Error converting PDF file: {str(e)}")
            return None

    def convert_document(self, input_path: str) -> str:
        """
        Convert document based on file extension.

        Args:
            input_path: Path to the input document

        Returns:
            Path to the converted Markdown file, or None if conversion failed
        """
        if not self._validate_file(input_path):
            return None

        file_extension = os.path.splitext(input_path)[1].lower()

        if file_extension == '.docx':
            return self.docx_to_markdown(input_path)
        elif file_extension == '.pdf':
            return self.pdf_to_markdown(input_path)
        else:
            print(f"Unsupported file format: {file_extension}")
            return None