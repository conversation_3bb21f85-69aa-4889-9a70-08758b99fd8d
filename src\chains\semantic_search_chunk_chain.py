"""Semantic search chunk retriever chain."""

from config.settings import settings
from core.faiss_vector_store import get_faiss_vector_store
from llm.get_llm import get_embedding_function, get_model_function

# Initialize vector store and models (ensures configuration is validated)
_FAISS_STORE = get_faiss_vector_store()
# These ensure dependencies are initialized; suppress unused warnings by underscore
_EMBEDDINGS = get_embedding_function()
_MODEL = get_model_function()

# Create retriever from FAISS store using configured parameters
chunk_retriever = _FAISS_STORE.as_retriever(
    search_type=settings.RETRIEVAL_SEARCH_TYPE,
    search_kwargs={
        "k": settings.DEFAULT_K,
    },
)

# Fallback if no retriever is available
if chunk_retriever is None:

    class EmptyRetriever:
        def invoke(self, query):
            return []

        def get_relevant_documents(self, query):
            return []

    chunk_retriever = EmptyRetriever()

def get_chunk_retriever():
    """Get the chunk retriever for semantic search"""
    return chunk_retriever