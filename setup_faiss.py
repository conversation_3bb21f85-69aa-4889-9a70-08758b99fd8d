#!/usr/bin/env python3
"""
Setup script for migrating from Neo4j to FAISS
This script will:
1. Install required packages
2. Set up FAISS vector store
3. Test the setup
4. Provide instructions for next steps
"""

import subprocess
import sys
import os
from pathlib import Path

def run_command(command, description):
    """Run a shell command and handle errors"""
    print(f"\n🔧 {description}")
    print(f"Running: {command}")
    
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ Success: {description}")
        if result.stdout:
            print(f"Output: {result.stdout}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error: {description}")
        print(f"Error output: {e.stderr}")
        return False

def check_python_version():
    """Check if Python version is compatible"""
    print("🐍 Checking Python version...")
    version = sys.version_info
    if version.major == 3 and version.minor >= 8:
        print(f"✅ Python {version.major}.{version.minor}.{version.micro} is compatible")
        return True
    else:
        print(f"❌ Python {version.major}.{version.minor}.{version.micro} is not compatible. Need Python 3.8+")
        return False

def install_requirements():
    """Install required packages"""
    print("\n📦 Installing requirements...")
    
    # Install from requirements.txt
    success = run_command(
        "pip install -r requirements.txt",
        "Installing packages from requirements.txt"
    )
    
    if not success:
        print("⚠️ Failed to install from requirements.txt, trying individual packages...")
        
        # Essential packages
        packages = [
            "faiss-cpu==1.8.0",
            "langchain==0.3.11",
            "langchain_community==0.3.11",
            "langchain_openai==0.2.12",
            "fastapi==0.115.6",
            "uvicorn==0.32.1",
            "python-dotenv==1.0.1",
            "pydantic==2.9.2"
        ]
        
        for package in packages:
            run_command(f"pip install {package}", f"Installing {package}")
    
    return True

def setup_directories():
    """Create necessary directories"""
    print("\n📁 Setting up directories...")
    
    directories = [
        "faiss_index",
        "chat_sessions", 
        "temp_uploads"
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"✅ Created directory: {directory}")

def create_env_template():
    """Create .env template file"""
    print("\n⚙️ Creating .env template...")
    
    env_template = """# AI/LLM Settings
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# FAISS Settings
FAISS_INDEX_DIR=faiss_index
CHAT_SESSIONS_DIR=chat_sessions

# Model Settings
DEFAULT_MODEL=gpt-4o-mini
TEMPERATURE=0.0

# Retrieval Settings
DEFAULT_K=3
SIMILARITY_THRESHOLD=0.7

# File Upload Settings
MAX_FILE_SIZE=10485760
"""
    
    env_file = Path(".env")
    if not env_file.exists():
        with open(env_file, "w") as f:
            f.write(env_template)
        print("✅ Created .env template file")
        print("⚠️ Please edit .env file and add your API keys!")
    else:
        print("ℹ️ .env file already exists, skipping...")

def test_imports():
    """Test if all required modules can be imported"""
    print("\n🧪 Testing imports...")
    
    test_modules = [
        ("faiss", "FAISS"),
        ("langchain", "LangChain"),
        ("langchain_community", "LangChain Community"),
        ("langchain_openai", "LangChain OpenAI"),
        ("fastapi", "FastAPI"),
        ("uvicorn", "Uvicorn"),
        ("dotenv", "Python Dotenv"),
        ("pydantic", "Pydantic")
    ]
    
    all_passed = True
    for module, name in test_modules:
        try:
            __import__(module)
            print(f"✅ {name} imported successfully")
        except ImportError as e:
            print(f"❌ Failed to import {name}: {e}")
            all_passed = False
    
    return all_passed

def test_faiss_basic():
    """Test basic FAISS functionality"""
    print("\n🔬 Testing FAISS basic functionality...")
    
    try:
        import faiss
        import numpy as np
        
        # Create a simple test index
        dimension = 128
        index = faiss.IndexFlatL2(dimension)
        
        # Add some test vectors
        test_vectors = np.random.random((10, dimension)).astype('float32')
        index.add(test_vectors)
        
        # Search
        query = np.random.random((1, dimension)).astype('float32')
        distances, indices = index.search(query, 3)
        
        print(f"✅ FAISS test passed - found {len(indices[0])} results")
        return True
        
    except Exception as e:
        print(f"❌ FAISS test failed: {e}")
        return False

def run_migration_script():
    """Run the migration script to add sample data"""
    print("\n🔄 Running migration script...")
    
    if os.path.exists("migrate_to_faiss.py"):
        success = run_command(
            "python migrate_to_faiss.py",
            "Running migration script with sample data"
        )
        return success
    else:
        print("⚠️ Migration script not found, skipping...")
        return True

def print_next_steps():
    """Print next steps for the user"""
    print("\n" + "="*60)
    print("🎉 SETUP COMPLETED!")
    print("="*60)
    
    print("\n📋 NEXT STEPS:")
    print("1. Edit .env file and add your OpenAI API key")
    print("2. Start the application:")
    print("   cd src")
    print("   python main_simplified.py")
    print("   # or")
    print("   uvicorn main_simplified:app --reload")
    print("\n3. Test the API:")
    print("   - Upload documents: POST http://localhost:8000/upload")
    print("   - Chat: POST http://localhost:8000/chat")
    print("   - Stats: GET http://localhost:8000/stats")
    print("\n4. Optional - Add your data:")
    print("   python migrate_to_faiss.py your_data.json")
    
    print("\n📚 DOCUMENTATION:")
    print("   - Read FAISS_SETUP.md for detailed instructions")
    print("   - API docs: http://localhost:8000/docs (when server is running)")
    
    print("\n🆘 TROUBLESHOOTING:")
    print("   - Run: python test_faiss.py")
    print("   - Check logs for any errors")
    print("   - Ensure all environment variables are set")

def main():
    """Main setup function"""
    print("🚀 THAI BINH CHATBOT - NEO4J TO FAISS MIGRATION SETUP")
    print("="*60)
    
    # Check Python version
    if not check_python_version():
        return False
    
    # Install requirements
    install_requirements()
    
    # Setup directories
    setup_directories()
    
    # Create env template
    create_env_template()
    
    # Test imports
    if not test_imports():
        print("\n❌ Some imports failed. Please check the error messages above.")
        return False
    
    # Test FAISS
    if not test_faiss_basic():
        print("\n❌ FAISS test failed. Please check your installation.")
        return False
    
    # Run migration (optional)
    run_migration_script()
    
    # Print next steps
    print_next_steps()
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n✅ Setup completed successfully!")
            sys.exit(0)
        else:
            print("\n❌ Setup failed. Please check the errors above.")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n⚠️ Setup interrupted by user.")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error during setup: {e}")
        sys.exit(1)
