import os
import yaml
from typing import Optional, List, Dict, Any
from pathlib import Path


class Settings:
    """Configuration settings for the chatbot application loaded from cfg.yml"""
    
    def __init__(self, config_file: str = "cfg.yml"):
        """Initialize settings from YAML configuration file"""
        self.config_file = config_file
        self._config = self._load_config()
        
        # Project settings
        self.PROJECT_NAME: str = self._get_nested_value("project.name", "Thai Binh Chatbot")
        self.VERSION: str = self._get_nested_value("project.version", "2.0.0")
        self.API_V1_STR: str = self._get_nested_value("project.api_version", "/api/v1")
        
        # AI/LLM settings
        self.ANTHROPIC_API_KEY: Optional[str] = self._get_nested_value("ai.anthropic_api_key") or os.getenv("ANTHROPIC_API_KEY")
        self.OPENAI_API_KEY: Optional[str] = self._get_nested_value("ai.openai_api_key") or os.getenv("OPENAI_API_KEY")
        self.DEFAULT_MODEL: str = self._get_nested_value("ai.default_model", "gpt-4o-mini")
        self.AGENT_MODEL: str = self._get_nested_value("ai.agent_model", "gpt-4o-mini")
        self.TEST_MODEL: str = self._get_nested_value("ai.test_model", "gpt-4o-mini")
        self.EMBEDDING_MODEL: str = self._get_nested_value("ai.embedding_model", "text-embedding-3-small")
        self.TEMPERATURE: float = self._get_nested_value("ai.temperature", 0.0)
        self.MAX_TOKENS: int = self._get_nested_value("ai.max_tokens", 4000)
        
        # FAISS Vector Store settings
        self.FAISS_INDEX_DIR: str = self._get_nested_value("vector_store.index_dir", "faiss_index")
        self.VECTOR_STORE_TYPE: str = self._get_nested_value("vector_store.type", "faiss")
        
        # Vietnamese Embedding Service settings
        self.VIETNAMESE_EMBEDDING_SERVICE_URL: str = self._get_nested_value("embedding_service.url", "http://localhost:2603")
        self.USE_VIETNAMESE_EMBEDDING: bool = self._get_nested_value("embedding_service.enabled", True)
        self.EMBEDDING_SERVICE_TIMEOUT: int = self._get_nested_value("embedding_service.timeout", 30)
        
        # Chat settings
        self.CHAT_SESSIONS_DIR: str = self._get_nested_value("chat.sessions_dir", "chat_sessions")
        self.MEMORY_TYPE: str = self._get_nested_value("chat.memory_type", "file")
        self.MAX_HISTORY_LENGTH: int = self._get_nested_value("chat.max_history_length", 50)
        
        # Retrieval settings
        self.RETRIEVAL_SEARCH_TYPE: str = self._get_nested_value(
            "retrieval.search_type", "similarity"
        )
        self.DEFAULT_K: int = self._get_nested_value("retrieval.default_k", 3)
        self.SIMILARITY_THRESHOLD: float = self._get_nested_value(
            "retrieval.similarity_threshold", 0.7
        )
        self.MAX_RESULTS: int = self._get_nested_value(
            "retrieval.max_results", 10
        )

        # Chunking settings
        self.CHUNKING_DEFAULT_SIZE: int = self._get_nested_value("chunking.default.chunk_size", 1000)
        self.CHUNKING_DEFAULT_OVERLAP: int = self._get_nested_value("chunking.default.chunk_overlap", 100)
        self.CHUNKING_PDF_SIZE: int = self._get_nested_value("chunking.pdf.chunk_size", 1200)
        self.CHUNKING_PDF_OVERLAP: int = self._get_nested_value("chunking.pdf.chunk_overlap", 120)
        self.CHUNKING_WEB_SIZE: int = self._get_nested_value("chunking.web.chunk_size", 800)
        self.CHUNKING_WEB_OVERLAP: int = self._get_nested_value("chunking.web.chunk_overlap", 80)
        self.CHUNKING_JSON_SIZE: int = self._get_nested_value("chunking.json.chunk_size", 1000)
        self.CHUNKING_JSON_OVERLAP: int = self._get_nested_value("chunking.json.chunk_overlap", 100)
        self.CHUNKING_MARKDOWN_SIZE: int = self._get_nested_value("chunking.markdown.chunk_size", 1200)
        self.CHUNKING_MARKDOWN_OVERLAP: int = self._get_nested_value("chunking.markdown.chunk_overlap", 150)
        self.VIETNAMESE_SEPARATORS: List[str] = self._get_nested_value("chunking.vietnamese_separators", [
            "\n# ", "\n## ", "\n### ", "\n#### ", "\n##### ", "\n###### ",
            "\n\n", "\n", ". ", "! ", "? ", "。", "！", "？", "; ", ": ", ", ",
            " và ", " nhưng ", " tuy nhiên ", " ", ""
        ])
        
        # File upload settings
        self.MAX_FILE_SIZE: int = self._get_nested_value("file_upload.max_file_size", 10485760)
        self.ALLOWED_EXTENSIONS: List[str] = self._get_nested_value("file_upload.allowed_extensions", ["pdf", "txt", "docx", "json"])
        self.TEMP_DIR: str = self._get_nested_value("file_upload.temp_dir", "/tmp")

        # Async utilities settings
        self.ASYNC_MAX_RETRIES: int = self._get_nested_value("async_utils.max_retries", 3)
        self.ASYNC_RETRY_DELAY: int = self._get_nested_value("async_utils.retry_delay", 1)
        self.ASYNC_TIMEOUT: float = self._get_nested_value("async_utils.timeout", 30.0)
        
        # Server settings
        self.HOST: str = self._get_nested_value("server.host", "0.0.0.0")
        self.PORT: int = self._get_nested_value("server.port", 2603)
        self.DEBUG: bool = self._get_nested_value("server.debug", False)
        self.CORS_ORIGINS: List[str] = self._get_nested_value("server.cors_origins", ["*"])

        # Usage logging & API settings
        self.USAGE_LOG_DIR: str = self._get_nested_value("usage.log_dir", "usage_logs")
        self.USAGE_LOG_RETENTION_DAYS: int = self._get_nested_value("usage.retention_days", 30)
        self.USAGE_API_ENABLED: bool = self._get_nested_value("usage.api_enabled", True)

        # Frontend URLs
        self.API_URL: str = self._get_nested_value(
            "frontend.api_url", "https://thaibinh-chatbot.onrender.com"
        )
        self.CHATBOT_URL: str = self._get_nested_value(
            "frontend.chatbot_url",
            "https://thaibinh-chatbot.onrender.com/docs-rag-agent",
        )

        # Prompts settings
        self.AGENT_SYSTEM_PROMPT_PATH: str = self._get_nested_value(
            "prompts.agent_system_prompt_path",
            "src/config/prompts/agent_system_prompt.txt",
        )

        # Logging settings
        self.LOG_LEVEL: str = self._get_nested_value("logging.level", "INFO")
        self.LOG_FORMAT: str = self._get_nested_value(
            "logging.format",
            "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        )
    
    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from YAML file"""
        config_path = Path(self.config_file)
        
        # Try to find the config file in different locations
        search_paths = [
            config_path,  # Current directory
            Path.cwd() / self.config_file,  # Current working directory
            Path(__file__).parent.parent.parent / self.config_file,  # Project root
        ]
        
        for path in search_paths:
            if path.exists():
                try:
                    with open(path, 'r', encoding='utf-8') as f:
                        config = yaml.safe_load(f)
                        print(f"✅ Configuration loaded from: {path}")
                        return config or {}
                except Exception as e:
                    print(f"❌ Error loading config from {path}: {e}")
                    continue
        
        print(f"⚠️  Config file '{self.config_file}' not found. Using default values.")
        return {}
    
    def _get_nested_value(self, key_path: str, default: Any = None) -> Any:
        """Get nested value from config using dot notation (e.g., 'ai.openai_api_key')"""
        keys = key_path.split('.')
        value = self._config
        
        try:
            for key in keys:
                value = value[key]
            return value if value is not None and value != "" else default
        except (KeyError, TypeError):
            return default
    
    @property
    def faiss_index_path(self) -> str:
        """Get the full path to FAISS index directory"""
        return os.path.abspath(self.FAISS_INDEX_DIR)
    
    @property
    def chat_sessions_path(self) -> str:
        """Get the full path to chat sessions directory"""
        return os.path.abspath(self.CHAT_SESSIONS_DIR)
    
    @property
    def temp_dir_path(self) -> str:
        """Get the full path to temporary directory"""
        return os.path.abspath(self.TEMP_DIR)
    
    def validate_settings(self) -> bool:
        """Validate required settings"""
        if not self.OPENAI_API_KEY and not self.ANTHROPIC_API_KEY:
            raise ValueError("Either OPENAI_API_KEY or ANTHROPIC_API_KEY must be set in cfg.yml or environment variables")
        
        # Create directories if they don't exist
        os.makedirs(self.faiss_index_path, exist_ok=True)
        os.makedirs(self.chat_sessions_path, exist_ok=True)
        os.makedirs(self.temp_dir_path, exist_ok=True)
        
        return True
    
    def get_config_summary(self) -> Dict[str, Any]:
        """Get a summary of current configuration for debugging"""
        return {
            "project": {
                "name": self.PROJECT_NAME,
                "version": self.VERSION,
            },
            "vector_store": {
                "type": self.VECTOR_STORE_TYPE,
                "index_dir": self.faiss_index_path,
            },
            "models": {
                "default": self.DEFAULT_MODEL,
                "agent": self.AGENT_MODEL,
                "embedding": self.EMBEDDING_MODEL,
            },
            "api_keys_set": {
                "openai": bool(self.OPENAI_API_KEY),
                "anthropic": bool(self.ANTHROPIC_API_KEY),
            },
            "server": {
                "host": self.HOST,
                "port": self.PORT,
                "debug": self.DEBUG,
            }
        }
    
    def reload_config(self) -> None:
        """Reload configuration from file"""
        self.__init__(self.config_file)


# Global settings instance
settings = Settings()

# Validate settings on import
try:
    settings.validate_settings()
    print("✅ Settings validated successfully")
    print(f"📋 Config summary: {settings.get_config_summary()}")
except Exception as e:
    print(f"❌ Settings validation failed: {e}")