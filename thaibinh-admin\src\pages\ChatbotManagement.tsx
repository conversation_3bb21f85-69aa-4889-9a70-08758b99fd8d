import React, { useState, useEffect } from 'react';
import {
  Card,
  CardBody,
  PageSection,
  Title,
  Tabs,
  Tab,
  TabTitleText,
  TextArea,
  Button,
  Form,
  FormGroup,
  TextInput,
} from '@patternfly/react-core';
import {
  Table,
  TableHeader,
  TableBody,
  TableVariant,
} from '@patternfly/react-table';

const ChatbotManagement: React.FC = () => {
  const [activeTabKey, setActiveTabKey] = useState<string | number>(0);
  const [chatHistory, setChatHistory] = useState<any[]>([]);
  const [settings, setSettings] = useState({
    apiKey: '',
    modelName: '',
    temperature: '0.7',
    maxTokens: '2000',
  });

  useEffect(() => {
    // Load chat history and settings
    loadChatHistory();
    loadSettings();
  }, []);

  const loadChatHistory = async () => {
    // TODO: Implement chat history loading
    setChatHistory([
      {
        id: 1,
        user: 'User1',
        message: 'What is <PERSON><PERSON> nien?',
        response: '<PERSON><PERSON> nien is...',
        timestamp: new Date().toISOString(),
      },
      // Add more mock data
    ]);
  };

  const loadSettings = async () => {
    // TODO: Implement settings loading
  };

  const handleSettingsUpdate = async () => {
    // TODO: Implement settings update
  };

  const handleTabClick = (event: React.MouseEvent<any>, tabIndex: string | number) => {
    setActiveTabKey(tabIndex);
  };

  const chatHistoryColumns = [
    'ID',
    'User',
    'Question',
    'Response',
    'Timestamp',
  ];

  return (
    <PageSection>
      <Title headingLevel="h1">Chatbot Management</Title>

      <Card>
        <CardBody>
          <Tabs
            activeKey={activeTabKey}
            onSelect={handleTabClick}
            isBox
          >
            <Tab 
              eventKey={0} 
              title={<TabTitleText>Chat History</TabTitleText>}
            >
              <Table
                aria-label="Chat history table"
                variant={TableVariant.compact}
                cells={chatHistoryColumns}
                rows={chatHistory.map(chat => ({
                  cells: [
                    chat.id,
                    chat.user,
                    chat.message,
                    chat.response,
                    chat.timestamp,
                  ]
                }))}
              >
                <TableHeader />
                <TableBody />
              </Table>
            </Tab>

            <Tab 
              eventKey={1} 
              title={<TabTitleText>Settings</TabTitleText>}
            >
              <Form>
                <FormGroup label="API Key" isRequired>
                  <TextInput
                    type="password"
                    value={settings.apiKey}
                    onChange={value => setSettings({...settings, apiKey: value})}
                  />
                </FormGroup>

                <FormGroup label="Model Name" isRequired>
                  <TextInput
                    type="text"
                    value={settings.modelName}
                    onChange={value => setSettings({...settings, modelName: value})}
                  />
                </FormGroup>

                <FormGroup label="Temperature">
                  <TextInput
                    type="number"
                    value={settings.temperature}
                    onChange={value => setSettings({...settings, temperature: value})}
                  />
                </FormGroup>

                <FormGroup label="Max Tokens">
                  <TextInput
                    type="number"
                    value={settings.maxTokens}
                    onChange={value => setSettings({...settings, maxTokens: value})}
                  />
                </FormGroup>

                <Button 
                  variant="primary" 
                  onClick={handleSettingsUpdate}
                >
                  Save Settings
                </Button>
              </Form>
            </Tab>
          </Tabs>
        </CardBody>
      </Card>
    </PageSection>
  );
};

export default ChatbotManagement; 