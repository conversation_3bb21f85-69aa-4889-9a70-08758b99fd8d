from langchain_openai import OpenAIEmbeddings, ChatOpenAI
from config.settings import settings
from core.custom_embeddings import VietnameseEmbeddingService

def get_embedding_function():
    """Get embedding function using Vietnamese Embedding Service or OpenAI based on configuration"""
    print("get_embedding_function")
    
    if settings.USE_VIETNAMESE_EMBEDDING:
        # Use Vietnamese Embedding Service
        print(f"Using Vietnamese Embedding Service at {settings.VIETNAMESE_EMBEDDING_SERVICE_URL}")
        
        try:
            vietnamese_service = VietnameseEmbeddingService(
                embedding_service_url=settings.VIETNAMESE_EMBEDDING_SERVICE_URL,
                timeout=settings.EMBEDDING_SERVICE_TIMEOUT,
                normalize=True
            )
            
            # Test the service with a simple query
            test_result = vietnamese_service.embed_query("test")
            if test_result and len(test_result) > 0:
                print("✅ Vietnamese Embedding Service verified")
                return vietnamese_service
            else:
                print("⚠️ Vietnamese service test returned empty result")
                raise Exception("Vietnamese service test failed")
                
        except Exception as e:
            print(f"⚠️ Vietnamese Embedding Service failed: {e}")
            
            if settings.OPENAI_API_KEY:
                print("🔄 Falling back to OpenAI embeddings...")
                embeddings = OpenAIEmbeddings(
                    openai_api_key=settings.OPENAI_API_KEY,
                    model=settings.EMBEDDING_MODEL
                )
                return embeddings
            else:
                raise ValueError("Vietnamese service failed and no OpenAI API key available for fallback")
    else:
        # Fallback to OpenAI embeddings
        if not settings.OPENAI_API_KEY:
            raise ValueError("Missing 'OPENAI_API_KEY' in cfg.yml or environment variables.")
        
        print("Using OpenAI Embeddings")
        # Create and return the embeddings object
        embeddings = OpenAIEmbeddings(
            openai_api_key=settings.OPENAI_API_KEY,
            model=settings.EMBEDDING_MODEL
        )
        return embeddings

def get_model_function():
    """Get main chat model using configuration from cfg.yml"""
    print("get llm ")
    
    if not settings.OPENAI_API_KEY:
        raise ValueError("Missing 'OPENAI_API_KEY' in cfg.yml or environment variables.")

    # Create and return the chat model object
    model = ChatOpenAI(
        openai_api_key=settings.OPENAI_API_KEY,
        model=settings.AGENT_MODEL,
        temperature=settings.TEMPERATURE,
    )
    return model

def generate_test_model_function():
    """Get test model using configuration from cfg.yml"""
    print("get test llm ")
    
    if not settings.OPENAI_API_KEY:
        raise ValueError("Missing 'OPENAI_API_KEY' in cfg.yml or environment variables.")

    # Create and return the chat model object
    model = ChatOpenAI(
        openai_api_key=settings.OPENAI_API_KEY,
        model=settings.TEST_MODEL,
        temperature=settings.TEMPERATURE,
    )
    return model

def get_eval_model_function():
    """Get evaluation model using configuration from cfg.yml"""
    print("get eval llm ")
    
    if not settings.OPENAI_API_KEY:
        raise ValueError("Missing 'OPENAI_API_KEY' in cfg.yml or environment variables.")

    # Create and return the chat model object
    model = ChatOpenAI(
        openai_api_key=settings.OPENAI_API_KEY,
        model=settings.DEFAULT_MODEL,
        temperature=settings.TEMPERATURE,
    )
    return model

