import React, { useState, useEffect } from 'react';
import {
  <PERSON>ton,
  Card,
  CardBody,
  FileUpload,
  Modal,
  PageSection,
  Tab,
  Tabs,
  TabTitleText,
  TextInput,
  Title,
  Toolbar,
  ToolbarContent,
  ToolbarItem,
} from '@patternfly/react-core';
import {
  Table,
  TableHeader,
  TableBody,
  TableVariant,
} from '@patternfly/react-table';
import { DocumentService } from '../services/DocumentService';

const DocumentManagement: React.FC = () => {
  const [activeTabKey, setActiveTabKey] = useState<string | number>(0);
  const [files, setFiles] = useState<any[]>([]);
  const [isUploadModalOpen, setIsUploadModalOpen] = useState(false);
  const [fileToUpload, setFileToUpload] = useState<File | null>(null);
  const [fileName, setFileName] = useState('');
  const [fileLink, setFileLink] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    loadFiles();
  }, []);

  const loadFiles = async () => {
    try {
      const response = await DocumentService.getFiles();
      setFiles(response.data);
    } catch (error) {
      console.error('Error loading files:', error);
    }
  };

  const columns = [
    'Document ID',
    'Document Name',
    'Link',
    'Type',
    'Actions'
  ];

  const handleFileUpload = async () => {
    if (!fileToUpload) return;

    setIsLoading(true);
    try {
      const formData = new FormData();
      formData.append('file', fileToUpload);
      formData.append('link', fileLink);
      formData.append('filename', fileName);

      await DocumentService.uploadFile(formData);
      setIsUploadModalOpen(false);
      loadFiles();
    } catch (error) {
      console.error('Error uploading file:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleFileChange = (file: File) => {
    setFileToUpload(file);
    setFileName(file.name);
  };

  const handleTabClick = (event: React.MouseEvent<any>, tabIndex: string | number) => {
    setActiveTabKey(tabIndex);
  };

  return (
    <PageSection>
      <Title headingLevel="h1">Document Management</Title>
      
      <Toolbar>
        <ToolbarContent>
          <ToolbarItem>
            <Button 
              variant="primary" 
              onClick={() => setIsUploadModalOpen(true)}
            >
              Upload Document
            </Button>
          </ToolbarItem>
        </ToolbarContent>
      </Toolbar>

      <Card>
        <CardBody>
          <Tabs
            activeKey={activeTabKey}
            onSelect={handleTabClick}
            isBox
          >
            <Tab 
              eventKey={0} 
              title={<TabTitleText>Documents</TabTitleText>}
            >
              <Table 
                aria-label="Documents table" 
                variant={TableVariant.compact}
                cells={columns}
                rows={files.map(file => ({
                  cells: [
                    file.file_id,
                    file.filename,
                    file.link,
                    file.type_document,
                    <Button 
                      key={`view-${file.file_id}`}
                      variant="secondary"
                      onClick={() => {/* Handle view */}}
                    >
                      View
                    </Button>
                  ]
                }))}
              >
                <TableHeader />
                <TableBody />
              </Table>
            </Tab>
          </Tabs>
        </CardBody>
      </Card>

      <Modal
        title="Upload Document"
        isOpen={isUploadModalOpen}
        onClose={() => setIsUploadModalOpen(false)}
        actions={[
          <Button 
            key="upload" 
            variant="primary" 
            onClick={handleFileUpload}
            isLoading={isLoading}
          >
            Upload
          </Button>,
          <Button 
            key="cancel" 
            variant="link" 
            onClick={() => setIsUploadModalOpen(false)}
          >
            Cancel
          </Button>
        ]}
      >
        <FileUpload
          id="file-upload"
          type="text"
          value={fileToUpload}
          filename={fileName}
          onChange={(value, filename) => handleFileChange(value as File)}
        />
        <TextInput
          type="text"
          value={fileLink}
          onChange={value => setFileLink(value)}
          placeholder="Enter file link"
        />
      </Modal>
    </PageSection>
  );
};

export default DocumentManagement; 