import os
import json
import logging
from typing import List, Dict, Any, Optional
from langchain_community.vectorstores import FAISS
from langchain_core.documents import Document
from llm.get_llm import get_embedding_function
from config.settings import settings

# Configure logging
logger = logging.getLogger(__name__)

class FAISSVectorStore:
    def __init__(self, persist_directory: str = None):
        if persist_directory is None:
            persist_directory = settings.FAISS_INDEX_DIR
        self.persist_directory = persist_directory
        self.vector_store = None
        self.embedding_function = get_embedding_function()
        self.TEXT_WORD_LIMIT = 4000
        # Create directory if it doesn't exist
        os.makedirs(self.persist_directory, exist_ok=True)
        # Try to load existing vector store
        self.load_vector_store()

    def _is_valid_text(self, text: Any) -> bool:
        """Kiểm tra text hợp lệ: phải là str, không rỗng sau strip"""
        if not isinstance(text, str):
            return False
        if not text or not text.strip():
            return False
        return True

    def add_documents(self, documents: List[Document]):
        """Add documents to the vector store"""
        if not documents:
            return
        # Filter and normalize documents
        valid_documents = []
        for doc in documents:
            text = doc.page_content
            if not self._is_valid_text(text):
                logger.warning(f"⚠️ Skipping document with invalid or empty content: {doc.metadata}")
                continue
            # Trim whitespace
            stripped = text.strip()
            # Truncate if too many words
            words = stripped.split()
            if len(words) > self.TEXT_WORD_LIMIT:
                stripped = ' '.join(words[:self.TEXT_WORD_LIMIT])
                logger.warning(
                    f"⚠️ Truncated document content for metadata {doc.metadata.get('chunk_id', 'N/A')} from {len(words)} to {self.TEXT_WORD_LIMIT} words"
                )
            doc.page_content = stripped
            valid_documents.append(doc)
        if not valid_documents:
            logger.warning("⚠️ No valid documents to add after validation and truncation")
            return
            
        texts = [doc.page_content.strip() for doc in valid_documents]
        metadatas = [doc.metadata for doc in valid_documents]
        
        try:
            if self.vector_store is None:
                # Create new vector store
                self.vector_store = FAISS.from_texts(
                    texts=texts,
                    embedding=self.embedding_function,
                    metadatas=metadatas
                )
                logger.info(f"✅ Created new vector store with {len(texts)} documents")
            else:
                # Try adding to existing vector store
                try:
                    self.vector_store.add_texts(texts=texts, metadatas=metadatas)
                    logger.info(f"✅ Added {len(texts)} documents to existing vector store")
                except AssertionError as ae:
                    # Dimension mismatch: recreate index
                    logger.warning(f"⚠️ Embedding dimension mismatch ({ae}), recreating vector store from scratch")
                    self.vector_store = FAISS.from_texts(
                        texts=texts,
                        embedding=self.embedding_function,
                        metadatas=metadatas
                    )
                    logger.info(f"✅ Recreated vector store with {len(texts)} documents due to dimension change")
            # Save after adding or recreating
            self.save_vector_store()
        except Exception:
            logger.error("❌ Error adding documents to vector store", exc_info=True)
            raise
    
    def add_texts(self, texts: List[str], metadatas: Optional[List[Dict]] = None):
        """Add texts directly to the vector store"""
        if not texts:
            return
        
        # Filter and normalize texts
        valid_texts = []
        valid_metadatas = []
        for i, text in enumerate(texts):
            if not self._is_valid_text(text):
                logger.warning(f"⚠️ Skipping invalid or empty text at index {i}")
                continue
            stripped = text.strip()
            words = stripped.split()
            if len(words) > self.TEXT_WORD_LIMIT:
                stripped = ' '.join(words[:self.TEXT_WORD_LIMIT])
                logger.warning(
                    f"⚠️ Truncated text at index {i} from {len(words)} to {self.TEXT_WORD_LIMIT} words"
                )
            valid_texts.append(stripped)
            md = metadatas[i] if metadatas and i < len(metadatas) else {}
            valid_metadatas.append(md)
        if not valid_texts:
            logger.warning("⚠️ No valid texts to add after validation and truncation")
            return
            
        try:
            if self.vector_store is None:
                self.vector_store = FAISS.from_texts(
                    texts=valid_texts,
                    embedding=self.embedding_function,
                    metadatas=valid_metadatas if valid_metadatas else None
                )
                logger.info(f"✅ Created new vector store with {len(valid_texts)} texts")
            else:
                self.vector_store.add_texts(texts=valid_texts, metadatas=valid_metadatas if valid_metadatas else None)
                logger.info(f"✅ Added {len(valid_texts)} texts to existing vector store")
            
            self.save_vector_store()
            
        except Exception as e:
            logger.error("❌ Error adding texts to vector store", exc_info=True)
            raise
    
    def similarity_search(self, query: str, k: int = 3) -> List[Document]:
        """Perform similarity search"""
        if not self.vector_store:
            return []
        
        if not query or not query.strip():
            logger.warning("⚠️ Empty query provided for similarity search")
            return []

        try:
            return self.vector_store.similarity_search(query.strip(), k=k)
        except Exception as e:
            logger.error(f"❌ Error in similarity search: {e}")
            return []
    
    def get_all_documents(self, max_docs: int = 1000) -> List[Document]:
        """
        Get all documents from the vector store.
        Since FAISS doesn't have a direct method, we use a broad search.
        """
        if not self.vector_store:
            return []
        
        try:
            # Use an empty query or common words to get diverse results
            results = self.vector_store.similarity_search("", k=max_docs)
            return results
        except:
            # Fallback: try with a space character
            try:
                results = self.vector_store.similarity_search(" ", k=max_docs)
                return results
            except:
                # If all else fails, try with a common Vietnamese word
                try:
                    results = self.vector_store.similarity_search("the", k=max_docs)
                    return results
                except:
                    return []
    
    def filter_documents_by_metadata(self, filter_dict: Dict[str, Any]) -> List[Document]:
        """
        Filter documents by metadata fields.
        """
        all_docs = self.get_all_documents()
        filtered_docs = []
        
        for doc in all_docs:
            match = True
            for key, value in filter_dict.items():
                if key not in doc.metadata or doc.metadata[key] != value:
                    match = False
                    break
            if match:
                filtered_docs.append(doc)
        
        return filtered_docs
    
    def similarity_search_with_score(self, query: str, k: int = 3, score_threshold: float = 0.0):
        """Perform similarity search with scores"""
        if not self.vector_store:
            return []
        
        results = self.vector_store.similarity_search_with_score(query, k=k)
        
        if score_threshold > 0:
            # Filter by score threshold (lower scores are better in FAISS)
            filtered_results = [(doc, score) for doc, score in results if score <= score_threshold]
            return [doc for doc, score in filtered_results]
        else:
            return [doc for doc, score in results]
    
    def as_retriever(self, search_type: str = "similarity", search_kwargs: Optional[Dict] = None):
        """Return a retriever interface"""
        if search_kwargs is None:
            search_kwargs = {"k": 3}
            
        if not self.vector_store:
            # Return empty retriever if no vector store
            return None
            
        return self.vector_store.as_retriever(
            search_type=search_type,
            search_kwargs=search_kwargs
        )
    
    def save_vector_store(self):
        """Save FAISS vector store to disk"""
        if self.vector_store:
            self.vector_store.save_local(self.persist_directory)
            logger.info(f"✅ Vector store saved to {self.persist_directory}")

    def load_vector_store(self):
        """Load FAISS vector store from disk"""
        try:
            index_path = os.path.join(self.persist_directory, "index.faiss")
            if os.path.exists(index_path):
                self.vector_store = FAISS.load_local(
                    self.persist_directory,
                    self.embedding_function,
                    allow_dangerous_deserialization=True
                )
                logger.info(f"✅ Vector store loaded from {self.persist_directory}")
                return True
        except Exception as e:
            logger.error(f"❌ Could not load vector store: {e}")
        return False
    
    def delete_vector_store(self):
        """Delete the vector store"""
        import shutil
        if os.path.exists(self.persist_directory):
            shutil.rmtree(self.persist_directory)
            logger.info(f"✅ Vector store deleted: {self.persist_directory}")
        self.vector_store = None
    
    def get_stats(self):
        """Get statistics about the vector store"""
        if not self.vector_store:
            return {"total_documents": 0}
        
        return {
            "total_documents": self.vector_store.index.ntotal,
            "embedding_dimension": self.vector_store.index.d
        }

# Global instance
_faiss_store = None

def get_faiss_vector_store() -> FAISSVectorStore:
    """Get or create the global FAISS vector store instance"""
    global _faiss_store
    if _faiss_store is None:
        _faiss_store = FAISSVectorStore()
    return _faiss_store
