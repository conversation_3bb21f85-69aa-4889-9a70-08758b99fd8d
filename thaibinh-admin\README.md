# Thai Binh Admin Dashboard

This is the admin dashboard for managing documents and chatbot settings for the Thai Binh project.

## Features

- Document Management
  - Upload documents (PDF, TXT, DOCX)
  - View and manage document chunks
  - Edit document metadata
  - Delete documents

- Chatbot Management
  - View chat history
  - Configure chatbot settings
  - Monitor chatbot performance

## Tech Stack

- React
- TypeScript
- PatternFly (UI Components)
- Axios (API Client)

## Getting Started

### Prerequisites

- Node.js (v14 or higher)
- npm or yarn

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd thaibinh-admin
```

2. Install dependencies:
```bash
npm install
```

3. Create a `.env` file in the root directory:
```env
REACT_APP_API_URL=http://localhost:8081
```

4. Start the development server:
```bash
npm start
```

The application will be available at `http://localhost:3000`.

### Building for Production

To create a production build:

```bash
npm run build
```

The build artifacts will be stored in the `build/` directory.

## Project Structure

```
src/
  ├── components/       # Reusable components
  ├── layouts/         # Layout components
  ├── pages/           # Page components
  ├── services/        # API services
  ├── utils/           # Utility functions
  ├── App.tsx          # Main application component
  └── index.tsx        # Application entry point
```

## API Integration

The admin dashboard communicates with a backend API running at `http://localhost:8081`. Make sure the API server is running before starting the admin dashboard.

## Contributing

1. Create a new branch for your feature
2. Make your changes
3. Submit a pull request

## License

This project is licensed under the MIT License. 