<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document Management for RAG Preparation</title>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <style>
        /* Customize the Tailwind CSS */
        .modal-overlay {
            background-color: rgba(0, 0, 0, 0.5);
        }
        .modal-content {
            max-width: 400px;
            padding: 20px;
        }
    </style>
</head>
<body class="bg-gray-100 text-gray-900 font-sans">

    <div class="container mx-auto p-6">

        <!-- Header Section -->
        <header class="text-center mb-6">
            <h1 class="text-3xl font-bold text-gray-800">Document Management for RAG Preparation</h1>
        </header>

        <!-- Button Area for Actions -->
        <div class="flex justify-center space-x-4 mb-8">
            <button id="list-files-btn" class="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition duration-300">
                <i class="fas fa-file-alt mr-2"></i> List Files
            </button>
            <button id="upload-file-btn" class="flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition duration-300">
                <i class="fas fa-upload mr-2"></i> Upload File
            </button>
            <button id="view-chunks-btn" class="flex items-center px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition duration-300">
                <i class="fas fa-cogs mr-2"></i> View Chunks
            </button>
            <button id="delete-file-btn" class="flex items-center px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition duration-300">
                <i class="fas fa-trash-alt mr-2"></i> Delete File
            </button>
        </div>

        <!-- Content Area -->
        <div id="content-area" class="space-y-6"></div>

    </div>

    <!-- File Upload Modal -->
    <div id="upload-modal" class="hidden modal-overlay fixed inset-0 flex justify-center items-center">
        <div class="modal-content bg-white rounded-lg shadow-lg p-6">
            <h2 class="text-xl font-semibold mb-4">Upload a File</h2>
            <input type="file" id="file-input" class="mb-4 w-full border border-gray-300 p-2 rounded">
            <div class="flex justify-between">
                <button id="upload-submit" class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 transition duration-300">
                    <i class="fas fa-upload mr-2"></i> Upload
                </button>
                <button id="close-modal" class="px-4 py-2 bg-gray-400 text-white rounded hover:bg-gray-500 transition duration-300">
                    Close
                </button>
            </div>
        </div>
    </div>

    <script>
        const API_URL = "http://localhost:8081";
        let currentPage = 1;
        let totalFiles = 0;

        // Show Upload File modal
        document.getElementById('upload-file-btn').addEventListener('click', () => {
            document.getElementById('upload-modal').classList.remove('hidden');
        });

        // Close Upload File modal
        document.getElementById('close-modal').addEventListener('click', () => {
            document.getElementById('upload-modal').classList.add('hidden');
        });

        // Handle file upload
        document.getElementById('upload-submit').addEventListener('click', async () => {
            const fileInput = document.getElementById('file-input');
            const file = fileInput.files[0];

            if (file) {
                const formData = new FormData();
                formData.append('file', file);

                try {
                    const response = await axios.post(`${API_URL}/files`, formData, {
                        headers: {
                            'Content-Type': 'multipart/form-data'
                        }
                    });
                    alert(`File uploaded: ${response.data.filename}`);
                    document.getElementById('upload-modal').classList.add('hidden');  // Hide modal after upload
                } catch (error) {
                    alert(`Error uploading file: ${error.response.data}`);
                }
            }
        });

        // List Files
        document.getElementById('list-files-btn').addEventListener('click', () => {
            fetchFiles(currentPage);
        });

        // Fetch files and render
        async function fetchFiles(page) {
            const response = await axios.get(`${API_URL}/files`);
            const files = response.data;
            totalFiles = files.length;
            renderFiles(files, page);
        }

        // Render files with pagination
        function renderFiles(files, page) {
            const start = (page - 1) * 5;
            const end = start + 5;
            const paginatedFiles = files.slice(start, end);
            let html = '<ul class="space-y-2">';
            paginatedFiles.forEach(file => {
                html += `<li class="p-4 bg-white shadow rounded-lg border border-gray-200">
                            <strong>File ID:</strong> ${file.file_id}
                        </li>`;
            });
            html += '</ul>';

            html += `
                <div class="flex justify-between mt-4">
                    <button id="prev-page" class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition duration-300" ${page === 1 ? 'disabled' : ''}>
                        <i class="fas fa-arrow-left"></i> Previous
                    </button>
                    <span class="text-lg font-semibold">Page ${page} of ${Math.ceil(totalFiles / 5)}</span>
                    <button id="next-page" class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition duration-300" ${page === Math.ceil(totalFiles / 5) ? 'disabled' : ''}>
                        Next <i class="fas fa-arrow-right"></i>
                    </button>
                </div>
            `;

            document.getElementById('content-area').innerHTML = html;

            document.getElementById('prev-page').addEventListener('click', () => {
                if (currentPage > 1) {
                    currentPage--;
                    fetchFiles(currentPage);
                }
            });

            document.getElementById('next-page').addEventListener('click', () => {
                if (currentPage < Math.ceil(totalFiles / 5)) {
                    currentPage++;
                    fetchFiles(currentPage);
                }
            });
        }

        // View Chunks
        document.getElementById('view-chunks-btn').addEventListener('click', () => {
            const fileId = prompt('Enter File ID');
            if (fileId) {
                fetchChunks(fileId);
            }
        });

        async function fetchChunks(fileId) {
            const response = await axios.get(`${API_URL}/files/${fileId}/chunks`);
            const chunks = response.data;
            let html = `<h2 class="text-xl font-semibold mb-4">Chunks for File ID: ${fileId}</h2>`;
            
            if (chunks.length === 0) {
                html += "<p>No chunks found for this file.</p>";
            } else {
                chunks.forEach(chunk => {
                    html += `
                        <div class="mb-4 p-4 bg-white shadow rounded-lg border border-gray-200">
                            <h3 class="text-lg font-semibold">Chunk Order: ${chunk.order}</h3>
                            <textarea class="w-full p-2 border rounded" readonly rows="4">${chunk.text}</textarea>
                        </div>
                    `;
                });
            }

            document.getElementById('content-area').innerHTML = html;
        }

        // Delete File
        document.getElementById('delete-file-btn').addEventListener('click', () => {
            const fileId = prompt('Enter File ID to delete');
            if (fileId) {
                deleteFile(fileId);
            }
        });

        async function deleteFile(fileId) {
            try {
                const response = await axios.delete(`${API_URL}/files/${fileId}`);
                alert('File deleted successfully.');
            } catch (error) {
                alert(`Error deleting file: ${error.response.data}`);
            }
        }
    </script>
</body>
</html>