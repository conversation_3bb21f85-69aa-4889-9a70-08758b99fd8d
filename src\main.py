# main.py

from fastapi import FastAP<PERSON>, File, UploadFile, Form, HTTPException, Path, Body
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
from core.faiss_vector_store import get_faiss_vector_store
from config.settings import settings
import logging
from logging.handlers import RotatingFileHandler
from typing import Optional
from contextlib import asynccontextmanager
from agents.rag_agent import get_agent
from utils import chunk_text, get_content_from_url, async_retry1
from models.schemas import Message, ChatResponse, DocumentReference
from asyncio import TimeoutError, wait_for
import uvicorn
from fastapi import status
from langchain_core.documents import Document
import uuid

from utils.structured_logging import get_logger as get_struct_logger
from utils.token_utils import estimate_tokens, get_model_token_limit, masked_user_id
from utils.usage_tracker import (
    start_query,
    set_query_tokens,
    set_prompt_stats,
    set_llm_usage,
    add_postproc_tokens,
    pop_summary,
    get as get_usage_record,
)
from utils.request_context import set_context as set_req_context

import os

# Configure logging: console (human-readable) + file (structured JSON lines)
# Ensure we write JSON payloads to a persistent file without extra prefixes
log_level_name = (settings.LOG_LEVEL or "INFO").upper()
root_level = getattr(logging, log_level_name, logging.INFO)
if settings.DEBUG:
    root_level = logging.DEBUG

console_handler = logging.StreamHandler()
console_handler.setLevel(root_level)
console_handler.setFormatter(logging.Formatter(settings.LOG_FORMAT))

file_handler = RotatingFileHandler(
    filename="api.log",
    maxBytes=5 * 1024 * 1024,  # 5 MB
    backupCount=3,
    encoding="utf-8",
)
# Capture debug details to file only when DEBUG is enabled
file_handler.setLevel(logging.DEBUG if settings.DEBUG else root_level)
# Emit raw JSON message only (StructuredLogger already serializes JSON)
file_handler.setFormatter(logging.Formatter("%(message)s"))

logging.basicConfig(
    level=root_level,
    handlers=[console_handler, file_handler],
)

logger = get_struct_logger(__name__)

# Initialize FAISS vector store
faiss_store = get_faiss_vector_store()

@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup actions
    logger.info("Starting up and ensuring FAISS vector store is ready.")
    # FAISS store is automatically initialized
    try:
        from utils.usage_storage import cleanup_old_logs
        cleanup_old_logs()
    except Exception:
        pass
    yield
    # Shutdown actions
    logger.info("Shutting down...")
    # FAISS doesn't need explicit cleanup

# Apply lifespan to the app
app = FastAPI(
    title=settings.PROJECT_NAME,
    description="Document Management & RAG Chatbot API using FAISS Vector Store",
    version=settings.VERSION,
    lifespan=lifespan
)

# Re-add middleware after reinitializing app
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# ============================
# Document Management Endpoints
# ============================

@app.post("/files", summary="Upload a new file")
async def create_file(
    file: UploadFile = File(...),
    link: str = Form(...),
    description: Optional[str] = Form(None),
    tags: Optional[str] = Form(None),
):
    """
    Upload a file, extract text, chunk it, and store in FAISS vector store.
    """
    try:
        # Save file temporarily
        if file.content_type not in ["application/pdf"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Unsupported file type."
            )

        temp_dir = settings.temp_dir_path
        os.makedirs(temp_dir, exist_ok=True)
        temp_file_path = os.path.join(temp_dir, file.filename)

        with open(temp_file_path, "wb") as f:
            content = await file.read()
            f.write(content)

        # Process chunks
        chunks = chunk_text(temp_file_path)

        # Prepare metadata
        upload_id = str(uuid.uuid4())
        base_metadata = {
            "filename": file.filename,
            "link": link,
            "description": description,
            "tags": tags.split(",") if tags else [],
            "type": "File",
            "upload_id": upload_id
        }

        # Create documents for FAISS
        documents = []
        for i, chunk_doc in enumerate(chunks):  # Renamed 'chunk' to 'chunk_doc' for clarity
            doc_metadata = base_metadata.copy()
            doc_metadata.update({
                "chunk_id": f"{file.filename}_chunk_{i}",
                "chunk_index": i
            })
            
            doc = Document(
                page_content=chunk_doc.page_content,  # Extract the string content
                metadata=doc_metadata
            )
            documents.append(doc)

        # Store in FAISS
        faiss_store.add_documents(documents)

        # Cleanup temporary file
        os.remove(temp_file_path)

        return {
            "filename": file.filename,
            "chunk_count": len(chunks),
            "link": link,
            "description": description,
            "tags": base_metadata["tags"],
            "status": "success"
        }

    except HTTPException as he:
        raise he  # Re-raise HTTP exceptions to be handled by FastAPI
    except Exception as e:
        logger.error(f"Unexpected error in create_file: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An unexpected error occurred."
        )

@app.post("/web-content", summary="Process web content and store in FAISS")
async def create_web_content(
    url: str = Form(...),
    type_document: Optional[str] = Form("Website"),
):
    """
    Fetch web content from the given URL, chunk it, and store in FAISS.
    """
    try:
        # Fetch content and chunk it
        content_data = get_content_from_url(url=url)
        
        # Handle different return formats from get_content_from_url
        if isinstance(content_data, dict):
            chunks = content_data.get("chunks", [])
            title = content_data.get("title", "Web Content")
        elif isinstance(content_data, list):
            chunks = content_data
            title = "Web Content"
        else:
            chunks = []
            title = "Web Content"
            
        print(f"Extracted {len(chunks)} chunks from URL")
        
        if not chunks:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to extract content from the URL."
            )

        # Prepare metadata for FAISS
        upload_id = str(uuid.uuid4())
        base_metadata = {
            "filename": title,
            "link": url,
            "type": type_document,
            "source": "web",
            "upload_id": upload_id
        }

        # Create documents for FAISS (handle both Document and str chunks)
        documents = []
        for i, chunk in enumerate(chunks):
            extra_md = {
                "chunk_id": f"{title}_chunk_{i}",
                "chunk_index": i,
            }
            if isinstance(chunk, Document):
                text = chunk.page_content
                md = {**base_metadata, **chunk.metadata, **extra_md}
            else:
                text = str(chunk)
                md = {**base_metadata, **extra_md}
            documents.append(Document(page_content=text, metadata=md))

        # Store in FAISS
        faiss_store.add_documents(documents)

        return {
            "filename": title,
            "message": "Content successfully processed and stored.",
            "chunk_count": len(chunks),
            "type_document": type_document,
            "url": url,
            "status": "success"
        }
    except HTTPException as he:
        raise he  # Re-raise HTTP exceptions to be handled by FastAPI
    except Exception as e:
        logger.error(f"Unexpected error in create_web_content: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An unexpected error occurred."
        )

@app.get("/stats", summary="Get vector store statistics")
async def get_vector_stats():
    """
    Get statistics about the FAISS vector store.
    """
    try:
        stats = faiss_store.get_stats()
        return {
            "total_documents": stats.get("total_documents", 0),
            "embedding_dimension": stats.get("embedding_dimension"),
            "vector_store_type": "FAISS"
        }
    except Exception as e:
        logger.error(f"Error in get_vector_stats: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/search", summary="Search documents in vector store")
async def search_documents(
    query: str = Body(..., embed=True, description="Search query"),
    k: int = Body(3, embed=True, description="Number of results to return")
):
    """
    Search for documents in the FAISS vector store.
    """
    try:
        results = faiss_store.similarity_search(query, k=k)
        
        formatted_results = []
        for doc in results:
            formatted_results.append({
                "content": doc.page_content,
                "metadata": doc.metadata
            })
        
        return {
            "query": query,
            "results": formatted_results,
            "count": len(formatted_results)
        }
    except Exception as e:
        logger.error(f"Error in search_documents: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# ============================
# File Management Endpoints for FAISS
# ============================

@app.get("/files", summary="List uploaded files")
async def list_files():
    """
    Get a list of uploaded files based on metadata in FAISS vector store.
    Returns unique files by upload_id and filename.
    """
    try:
        # Get all documents and extract unique file information
        stats = faiss_store.get_stats()
        
        if stats.get("total_documents", 0) == 0:
            return {"files": [], "total": 0}
        
        # Get all documents from FAISS
        all_results = faiss_store.get_all_documents()
        
        # Extract unique files by upload_id
        files_dict = {}
        for doc in all_results:
            metadata = doc.metadata
            upload_id = metadata.get("upload_id")
            filename = metadata.get("filename")
            
            if upload_id and upload_id not in files_dict:
                files_dict[upload_id] = {
                    "file_id": upload_id,
                    "filename": filename,
                    "link": metadata.get("link", ""),
                    "description": metadata.get("description", ""),
                    "tags": metadata.get("tags", []),
                    "type": metadata.get("type", "Unknown"),
                    "source": metadata.get("source", "upload"),
                    "chunk_count": 0
                }
            
            if upload_id:
                files_dict[upload_id]["chunk_count"] += 1
        
        files_list = list(files_dict.values())
        
        return {
            "files": files_list,
            "total": len(files_list)
        }
        
    except Exception as e:
        logger.error(f"Error in list_files: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/files/{file_id}/chunks", summary="Get chunks of a specific file")
async def get_file_chunks(file_id: str):
    """
    Retrieve chunks of a specific file by its upload_id (file_id).
    """
    try:
        # Filter documents by upload_id
        file_chunks_docs = faiss_store.filter_documents_by_metadata({"upload_id": file_id})
        
        if not file_chunks_docs:
            raise HTTPException(status_code=404, detail="File not found")
        
        file_chunks = []
        for doc in file_chunks_docs:
            file_chunks.append({
                "chunk_id": doc.metadata.get("chunk_id"),
                "chunk_index": doc.metadata.get("chunk_index", 0),
                "content": doc.page_content,
                "metadata": doc.metadata
            })
        
        # Sort by chunk_index
        file_chunks.sort(key=lambda x: x.get("chunk_index", 0))
        
        return {
            "file_id": file_id,
            "chunks": file_chunks,
            "total_chunks": len(file_chunks)
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in get_file_chunks: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/files/{file_id}/chunks/{chunk_id}", summary="Get a specific chunk")
async def get_specific_chunk(file_id: str, chunk_id: str):
    """
    Get a specific chunk by file_id (upload_id) and chunk_id.
    """
    try:
        # Filter documents by both upload_id and chunk_id
        matching_docs = faiss_store.filter_documents_by_metadata({
            "upload_id": file_id,
            "chunk_id": chunk_id
        })
        
        if not matching_docs:
            raise HTTPException(status_code=404, detail="Chunk not found")
        
        doc = matching_docs[0]  # Should be only one match
        return {
            "file_id": file_id,
            "chunk_id": chunk_id,
            "content": doc.page_content,
            "metadata": doc.metadata
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in get_specific_chunk: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.put("/files/{file_id}/chunks/{chunk_id}", summary="Update a specific chunk")
async def edit_file_chunk(
    file_id: str = Path(..., description="ID of the file (upload_id)"),
    chunk_id: str = Path(..., description="ID of the chunk to be edited"),
    new_text: str = Body(..., embed=True, description="New text for the chunk"),
):
    """
    Update the text of a specific chunk. 
    Note: This recreates the entire vector store which can be slow for large datasets.
    """
    try:
        # Get all documents
        all_results = faiss_store.get_all_documents()
        
        # Find and update the specific chunk
        updated_documents = []
        chunk_found = False
        
        for doc in all_results:
            if (doc.metadata.get("upload_id") == file_id and 
                doc.metadata.get("chunk_id") == chunk_id):
                # Update this chunk
                updated_doc = Document(
                    page_content=new_text,
                    metadata=doc.metadata
                )
                updated_documents.append(updated_doc)
                chunk_found = True
            else:
                # Keep other chunks as they are
                updated_documents.append(doc)
        
        if not chunk_found:
            raise HTTPException(status_code=404, detail="Chunk not found")
        
        # Recreate the vector store with updated documents
        # This is expensive but necessary with FAISS
        faiss_store.delete_vector_store()
        if updated_documents:
            faiss_store.add_documents(updated_documents)
        
        return {
            "message": "Chunk updated successfully",
            "file_id": file_id,
            "chunk_id": chunk_id,
            "new_content": new_text[:100] + "..." if len(new_text) > 100 else new_text
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in edit_file_chunk: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.delete("/files/{file_id}", summary="Delete a specific file")
async def delete_file(file_id: str):
    """
    Delete a specific file by its upload_id.
    This removes all chunks associated with the file_id.
    """
    try:
        # Get all documents
        all_results = faiss_store.get_all_documents()
        
        # Filter out documents with the specific upload_id
        remaining_documents = []
        deleted_count = 0
        
        for doc in all_results:
            if doc.metadata.get("upload_id") == file_id:
                deleted_count += 1
            else:
                remaining_documents.append(doc)
        
        if deleted_count == 0:
            raise HTTPException(status_code=404, detail="File not found")
        
        # Recreate the vector store without the deleted file
        faiss_store.delete_vector_store()
        if remaining_documents:
            faiss_store.add_documents(remaining_documents)
        
        return {
            "message": f"File {file_id} deleted successfully",
            "deleted_chunks": deleted_count,
            "remaining_documents": len(remaining_documents)
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in delete_file: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


# ============================
# Chatbot Endpoints
# ============================

@async_retry1(max_retries=3, delay=1)
async def invoke_agent_with_retry(message: Message, timeout: int = 30):
    """
    Retry the agent if a tool fails to run. Helps with intermittent connection issues.
    """
    logger.info("Invoking chat agent with retry mechanism.")
    try:
        # Adding a timeout to ensure the query does not hang indefinitely
        response = await wait_for(
            get_agent().ainvoke(
                {"input": message.text},
                {"configurable": {"session_id": message.session}}
            ),
            timeout=timeout
        )
        return response
    except TimeoutError:
        logger.error(f"Query timed out after {timeout} seconds.")
        raise
    except Exception as e:
        logger.error(f"Error invoking agent: {e}")
        raise


@app.get("/", summary="API Status")
async def get_status():
    """
    Check the status of the API.
    """
    return {"status": "running"}

# ============================
# Usage Analytics API
# ============================
from datetime import datetime as _dt
from fastapi import Query
from utils.usage_storage import iter_records, aggregate_summary, cost_breakdown


def _parse_date(v: str) -> _dt:
    return _dt.fromisoformat(v)


@app.get("/api/usage/summary")
async def usage_summary(
    start_date: str = Query(None, description="ISO date/time start"),
    end_date: str = Query(None, description="ISO date/time end"),
    user_id: str = Query(None),
    session_id: str = Query(None),
    model: str = Query(None),
    group_by: str = Query("day", pattern="^(day|month|year)$"),
):
    if not settings.USAGE_API_ENABLED:
        raise HTTPException(status_code=404, detail="Usage API disabled")
    try:
        sd = _parse_date(start_date) if start_date else None
        ed = _parse_date(end_date) if end_date else None
    except Exception:
        raise HTTPException(status_code=400, detail="Invalid date format")
    recs = iter_records(sd, ed, user_id=user_id, session_id=session_id, model=model)
    summary = aggregate_summary(recs, group_by=group_by)
    return summary


@app.get("/api/usage/details")
async def usage_details(
    start_date: str = Query(None),
    end_date: str = Query(None),
    user_id: str = Query(None),
    session_id: str = Query(None),
    model: str = Query(None),
    limit: int = Query(50, ge=1, le=1000),
    offset: int = Query(0, ge=0),
):
    if not settings.USAGE_API_ENABLED:
        raise HTTPException(status_code=404, detail="Usage API disabled")
    try:
        sd = _parse_date(start_date) if start_date else None
        ed = _parse_date(end_date) if end_date else None
    except Exception:
        raise HTTPException(status_code=400, detail="Invalid date format")
    recs = list(iter_records(sd, ed, user_id=user_id, session_id=session_id, model=model))
    total = len(recs)
    page = recs[offset: offset + limit]
    return {"total": total, "limit": limit, "offset": offset, "records": page}


@app.get("/api/usage/costs")
async def usage_costs(
    start_date: str = Query(None),
    end_date: str = Query(None),
    user_id: str = Query(None),
    session_id: str = Query(None),
    model: str = Query(None),
):
    if not settings.USAGE_API_ENABLED:
        raise HTTPException(status_code=404, detail="Usage API disabled")
    try:
        sd = _parse_date(start_date) if start_date else None
        ed = _parse_date(end_date) if end_date else None
    except Exception:
        raise HTTPException(status_code=400, detail="Invalid date format")
    recs = iter_records(sd, ed, user_id=user_id, session_id=session_id, model=model)
    return cost_breakdown(recs)


@app.post("/docs-rag-agent", response_model=ChatResponse, summary="Chat with the RAG Agent")
async def ask_docs_agent(message: Message) -> ChatResponse:
    """
    Interact with the RAG chatbot.
    """
    # 1. User Query Reception
    # Generate IDs and mask sensitive identifiers
    import uuid as _uuid
    query_id = str(_uuid.uuid4())
    user_id_masked = masked_user_id(getattr(message, "session", ""))  # using session as proxy
    set_req_context(query_id, user_id_masked, getattr(message, "session", ""))

    logger.info(
        "query_received",
        user_id=user_id_masked,
        query_id=query_id,
        timestamp=None,
    )
    # Begin usage record
    start_query(query_id=query_id, user_id=user_id_masked, session_id=getattr(message, "session", ""), model_name=settings.AGENT_MODEL)

    qlen = len(message.text or "")
    q_tokens_before = estimate_tokens(message.text or "")
    logger.debug(
        "query_debug",
        query_id=query_id,
        query_length=qlen,
        est_tokens=q_tokens_before,
        user_query=message.text or "",
    )

    # Enforce max input tokens
    try:
        model_limit = get_model_token_limit(settings.AGENT_MODEL)
    except Exception:
        model_limit = settings.MAX_TOKENS

    # Reserve budget for prompt and output
    reserve_for_prompt = 512
    reserve_for_output = 512
    max_input_tokens = max(1, model_limit - reserve_for_prompt - reserve_for_output)

    text = message.text or ""
    if q_tokens_before > max_input_tokens:
        # Trim input conservatively
        avg_chars_per_token = 4
        allowed_chars = max_input_tokens * avg_chars_per_token
        text = text[:allowed_chars]
        message.text = text
        logger.warn(
            "query_trimmed",
            query_id=query_id,
            before_tokens=q_tokens_before,
            after_tokens=max_input_tokens,
        )
    q_tokens_after = estimate_tokens(message.text or "")

    # 2. Query Preprocessing (placeholder: language detection/keywords)
    # In this codebase, we don't have dedicated preprocessing. We log structure only.
    logger.debug(
        "preprocess_result",
        query_id=query_id,
        language="vi",  # heuristic default
        keywords=[],
        question_type="unknown",
    )
    # Spam/injection detection stubs
    if any(x in (message.text or "").lower() for x in ["system:", "ignore previous", "\n```"]):
        logger.warn("anomalous_query", query_id=query_id, reason="potential_prompt_injection")

    # Track tokens before/after preprocess
    set_query_tokens(query_id, before=q_tokens_before, after=q_tokens_after)

    try:
        # 3-6. Invoke agent with retry mechanism (retrieval, reranking, prompt, LLM)
        import time as _time
        t0 = _time.time()
        query_response = await invoke_agent_with_retry(message)
        latency_ms = int((_time.time() - t0) * 1000)

        # We can't get exact token counts from LangChain here without callbacks; estimate
        input_tokens_est = q_tokens_after  # context/prompt tracked from tools
        output_tokens_est = estimate_tokens(str(query_response.get("output", ""))) if isinstance(query_response, dict) else 0
        set_llm_usage(query_id, input_tokens=input_tokens_est, output_tokens=output_tokens_est, latency_ms=latency_ms, status="ok")
        logger.info(
            "llm_call",
            query_id=query_id,
            model_name=settings.AGENT_MODEL,
            inference_ms=latency_ms,
            status="ok",
        )

        # Additional logging: summarize retrieved docs and final prompt captured upstream
        try:
            usage = get_usage_record(query_id)
            if usage and getattr(usage, "retrieved_docs", None) is not None:
                logger.debug(
                    "retrieval_summary",
                    query_id=query_id,
                    retrieved_count=len(usage.retrieved_docs or []),
                    retrieved_tokens_total=getattr(usage, "retrieved_tokens_total", 0),
                    context_tokens_after_filter=getattr(usage, "context_tokens_after_filter", 0),
                )
        except Exception:
            pass

        if query_response is None:
            logger.error("invoke_agent_with_retry returned None after all retry attempts.")
            from models.schemas import ChatResponse as _ChatResponse
            return _ChatResponse(
                success=False,
                intermediate_steps=["No response from the agent."],
                output="Failed to get a response."
            )

        # Ensure 'intermediate_steps' exists in the response
        if isinstance(query_response, dict) and "intermediate_steps" not in query_response:
            logger.error("Invalid response structure: 'intermediate_steps' key is missing.")
            query_response["intermediate_steps"] = ["No intermediate steps available."]

        # Process intermediate steps into strings if necessary
        try:
            if isinstance(query_response, dict):
                query_response["intermediate_steps"] = [
                    str(step) for step in query_response.get("intermediate_steps", [])
                ]
        except Exception as e:
            logger.error(f"Error processing 'intermediate_steps': {e}")
            if isinstance(query_response, dict):
                query_response["intermediate_steps"] = ["Error processing intermediate steps."]

        # 7. Response Post-processing (none specific; placeholders)
        logger.debug("postprocess_steps", query_id=query_id, steps=[])

        # Try to extract references from tool output if present
        references: list[dict] = []
        try:
            marker = "REFERENCES_JSON:"
            def _extract_json_array(text: str) -> str | None:
                # Find the first '[' after the marker and return a bracket-balanced slice
                start = text.find("[")
                if start == -1:
                    return None
                depth = 0
                in_string = False
                escape = False
                for i in range(start, len(text)):
                    ch = text[i]
                    if in_string:
                        if escape:
                            escape = False
                        elif ch == "\\":
                            escape = True
                        elif ch == '"':
                            in_string = False
                        continue
                    else:
                        if ch == '"':
                            in_string = True
                        elif ch == '[':
                            depth += 1
                        elif ch == ']':
                            depth -= 1
                            if depth == 0:
                                return text[start:i+1]
                return None

            import json as _json
            steps_list = (query_response.get("intermediate_steps", []) if isinstance(query_response, dict) else [])
            for step_text in steps_list:
                if marker in step_text:
                    after = step_text.split(marker, 1)[1].strip()
                    arr_str = _extract_json_array(after)
                    parsed = None
                    if arr_str:
                        try:
                            parsed = _json.loads(arr_str)
                        except Exception:
                            parsed = None
                    if parsed is None:
                        # Fallback: try a conservative trim of the first line / before trailing parentheses
                        candidate = after.splitlines()[0].split(")", 1)[0].strip().rstrip("'").rstrip('"')
                        try:
                            parsed = _json.loads(candidate)
                        except Exception:
                            continue
                    if isinstance(parsed, list):
                        for item in parsed:
                            try:
                                filename = item.get("filename")
                                link = item.get("link")
                                if filename:
                                    references.append({"filename": str(filename), "link": link})
                            except Exception:
                                continue
        except Exception as e:
            logger.warn("references_parse_failed", query_id=query_id, error=str(e))

        # Construct the final response object including references
        output_text = query_response.get("output") if isinstance(query_response, dict) else None
        if not output_text:
            output_text = (
                "Hiện tại tôi đang không có câu trả lời cho câu hỏi bạn cần. Nếu bạn có thêm câu hỏi nào khác về Đoàn Thanh niên Cộng sản Hồ Chí Minh hoặc các nội dung liên quan, xin vui lòng cho tôi biết! "
            )
        from models.schemas import ChatResponse as _ChatResponse
        final_response = _ChatResponse(
            success=True,
            intermediate_steps=(query_response.get("intermediate_steps", []) if isinstance(query_response, dict) else []),
            output=output_text,
            references=[
                {"filename": r.get("filename"), "link": r.get("link")} for r in references
            ],
        )

        # 8. Response Delivery + usage summary
        logger.debug("response_debug", query_id=query_id, response_length=len(final_response.output))
        summary = pop_summary(query_id)
        logger.info("response_delivered", query_id=query_id, success=True, usage=summary)
        return final_response

    except TimeoutError:
        logger.error("llm_timeout", query_id=query_id)
        set_llm_usage(query_id, input_tokens=0, output_tokens=0, latency_ms=0, status="timeout")
        summary = pop_summary(query_id)
        logger.info("response_delivered", query_id=query_id, success=False, usage=summary)
        from models.schemas import ChatResponse as _ChatResponse
        return _ChatResponse(
            success=False,
            intermediate_steps=["Query timed out."],
            output="Query timed out."
        )
    except Exception as e:
        logger.error("unexpected_error", query_id=query_id, error=str(e))
        set_llm_usage(query_id, input_tokens=0, output_tokens=0, latency_ms=0, status="error")
        summary = pop_summary(query_id)
        logger.info("response_delivered", query_id=query_id, success=False, usage=summary)
        from models.schemas import ChatResponse as _ChatResponse
        return _ChatResponse(
            success=False,
            intermediate_steps=["An unexpected error occurred."],
            output=str(e)
        )


if __name__ == "__main__":
    uvicorn.run(app, host=settings.HOST, port=settings.PORT)