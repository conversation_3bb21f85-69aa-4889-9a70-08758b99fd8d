"""
Utils package for Thai Binh Chatbot
Provides utilities for document loading, chunking, and async operations
"""

# Import main functions from each module
from .chunking_config import (
    ChunkingConfig,
    get_chunking_config,
    chunking_config
)

from .load_pdf import (
    chunk_text,
    chunk_pdf_with_custom_config
)

from .load_html import (
    get_content_from_url,
    get_content_from_url_with_custom_config,
    chunk_text_html,
    bs4_extractor
)

from .load_json import (
    get_chunk_with_json,
    chunk_json_content,
    chunk_json_to_documents,
    metadata_func
)

from .async_utils import (
    async_retry1,
    async_timeout,
    async_retry_with_timeout
)

# Export all main functions
__all__ = [
    # Chunking configuration
    'ChunkingConfig',
    'get_chunking_config',
    'chunking_config',
    
    # PDF utilities
    'chunk_text',
    'chunk_pdf_with_custom_config',
    
    # HTML/Web utilities
    'get_content_from_url',
    'get_content_from_url_with_custom_config',
    'chunk_text_html',
    'bs4_extractor',
    
    # JSON utilities
    'get_chunk_with_json',
    'chunk_json_content',
    'chunk_json_to_documents',
    'metadata_func',
    
    # Async utilities
    'async_retry1',
    'async_timeout',
    'async_retry_with_timeout'
]
