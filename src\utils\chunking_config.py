"""
Chunking configuration utility for Thai Binh Chatbot
Provides centralized chunking settings and text splitter creation
"""

from typing import List, Dict, Any, Optional
from langchain.text_splitter import RecursiveCharacterTextSplitter
from config.settings import settings
import re
import logging

logger = logging.getLogger(__name__)

class ChunkingConfig:
    """Centralized chunking configuration manager"""
    
    def __init__(self):
        self.settings = settings
        
    def get_chunk_config(self, content_type: str = "default") -> Dict[str, Any]:
        """
        Get chunking configuration for specific content type
        
        Args:
            content_type: Type of content ('pdf', 'web', 'json', 'markdown', 'default')
            
        Returns:
            Dictionary with chunk_size and chunk_overlap
        """
        config_map = {
            "pdf": {
                "chunk_size": self.settings.CHUNKING_PDF_SIZE,
                "chunk_overlap": self.settings.CHUNKING_PDF_OVERLAP
            },
            "web": {
                "chunk_size": self.settings.CHUNKING_WEB_SIZE,
                "chunk_overlap": self.settings.CHUNKING_WEB_OVERLAP
            },
            "html": {  # Alias for web
                "chunk_size": self.settings.CHUNKING_WEB_SIZE,
                "chunk_overlap": self.settings.CHUNKING_WEB_OVERLAP
            },
            "json": {
                "chunk_size": self.settings.CHUNKING_JSON_SIZE,
                "chunk_overlap": self.settings.CHUNKING_JSON_OVERLAP
            },
            "markdown": {
                "chunk_size": self.settings.CHUNKING_MARKDOWN_SIZE,
                "chunk_overlap": self.settings.CHUNKING_MARKDOWN_OVERLAP
            },
            "default": {
                "chunk_size": self.settings.CHUNKING_DEFAULT_SIZE,
                "chunk_overlap": self.settings.CHUNKING_DEFAULT_OVERLAP
            }
        }
        
        return config_map.get(content_type, config_map["default"])
    
    def get_vietnamese_separators(self) -> List[str]:
        """Get Vietnamese language optimized separators"""
        return self.settings.VIETNAMESE_SEPARATORS
    
    def create_text_splitter(self, content_type: str = "default", 
                           custom_separators: Optional[List[str]] = None) -> RecursiveCharacterTextSplitter:
        """
        Create a RecursiveCharacterTextSplitter with appropriate configuration
        
        Args:
            content_type: Type of content to optimize for
            custom_separators: Optional custom separators list
            
        Returns:
            Configured RecursiveCharacterTextSplitter instance
        """
        config = self.get_chunk_config(content_type)
        separators = custom_separators or self.get_vietnamese_separators()
        
        logger.info(f"Creating text splitter for {content_type}: "
                   f"chunk_size={config['chunk_size']}, "
                   f"chunk_overlap={config['chunk_overlap']}")
        
        return RecursiveCharacterTextSplitter(
            separators=separators,
            chunk_size=config["chunk_size"],
            chunk_overlap=config["chunk_overlap"],
            length_function=len,
            keep_separator=True,
            strip_whitespace=True,
            add_start_index=True,
        )
    
    def preprocess_vietnamese_text(self, text: str) -> str:
        """
        Preprocess Vietnamese text for better chunking
        
        Args:
            text: Input text to preprocess
            
        Returns:
            Preprocessed text
        """
        if not text or not isinstance(text, str):
            return ""
        
        # Normalize punctuation
        text = re.sub(r'[.]{2,}', '.', text)  # Multiple periods
        text = re.sub(r'[!]{2,}', '!', text)  # Multiple exclamations
        text = re.sub(r'[?]{2,}', '?', text)  # Multiple question marks
        
        # Normalize whitespace
        text = re.sub(r'\s+', ' ', text)
        
        # Handle common Vietnamese abbreviations
        abbreviations = {
            'TP.': 'Thành phố',
            'Q.': 'Quận',
            'P.': 'Phường',
            'TT.': 'Thị trấn',
            'Đ/c': 'Đồng chí',
            'UBND': 'Ủy ban Nhân dân',
            'TPHCM': 'Thành phố Hồ Chí Minh'
        }
        
        for abbr, full in abbreviations.items():
            text = text.replace(abbr, full)
        
        return text.strip()
    
    def validate_chunk_config(self, content_type: str) -> bool:
        """
        Validate chunking configuration for a content type
        
        Args:
            content_type: Type of content to validate
            
        Returns:
            True if configuration is valid, False otherwise
        """
        config = self.get_chunk_config(content_type)
        
        # Check if chunk_size is reasonable (between 100 and 8000)
        if not (100 <= config["chunk_size"] <= 8000):
            logger.warning(f"Chunk size {config['chunk_size']} for {content_type} "
                          f"is outside recommended range (100-8000)")
            return False
        
        # Check if overlap is reasonable (5-50% of chunk_size)
        overlap_ratio = config["chunk_overlap"] / config["chunk_size"]
        if not (0.05 <= overlap_ratio <= 0.5):
            logger.warning(f"Chunk overlap ratio {overlap_ratio:.2%} for {content_type} "
                          f"is outside recommended range (5%-50%)")
            return False
        
        return True
    
    def get_all_configs(self) -> Dict[str, Dict[str, Any]]:
        """Get all chunking configurations"""
        content_types = ["default", "pdf", "web", "json", "markdown"]
        return {ct: self.get_chunk_config(ct) for ct in content_types}


# Global instance
chunking_config = ChunkingConfig()


def get_chunking_config() -> ChunkingConfig:
    """Get the global chunking configuration instance"""
    return chunking_config
