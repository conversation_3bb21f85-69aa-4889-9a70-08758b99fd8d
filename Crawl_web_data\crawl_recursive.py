from langchain_community.document_loaders import RecursiveUrlLoader
import re
from langchain_core.load import dumpd, dumps, load, loads
from bs4 import BeautifulSoup
import json
import sys
import os

# Add src directory to Python path to import utils
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))
from utils import get_chunking_config

num = 0
def bs4_extractor(html: str) -> str:
    # Initialize BeautifulSoup with a valid parser
    soup = BeautifulSoup(html, "html.parser")  # Alternatively, use "lxml" or "html5lib" if installed

    # Find the first element with the class 'irs-blog-single-col'
    target_element = soup.find(class_="news-detail")

    if target_element:
        # Extract text from the target element
        text = target_element.get_text(separator=' ', strip=True)
        # Replace multiple whitespace characters with a single space
        cleaned_text = re.sub(r'\s+', ' ', text)
        # print(cleaned_text)

        return cleaned_text
    else:
        # Handle the case where the element isn't found
        return ""
def get_content_from_url(url):

    loader = RecursiveUrlLoader(
        url=url,
        max_depth=1,
        # use_async=False,
        # extractor=None,
        # metadata_extractor=None,
        # exclude_dirs=(),
        # timeout=10,
        check_response_status=True,
        continue_on_failure=True,
        extractor=bs4_extractor,
        # prevent_outside=True,
        # base_url=None,
        # ...
    )
    # Get chunking configuration for web content
    chunking_config = get_chunking_config()
    text_splitter = chunking_config.create_text_splitter("web")
    docs = loader.load()
    chunks = text_splitter.split_documents(docs)
    return chunks

# url  =  "https://doanthanhnien.vn/tin-tuc/cong-tac-giao-duc/gia-lai-nang-cao-ky-nang-nghiep-vu-cho-luc-luong-bao-cao-vien-can-bo-doan-chu-chot"
# docs = get_content_from_url(url)
# print(docs)

# with open('doanthanhnien.json', 'w', encoding="utf-8") as file:
#     json.dump(dict_representation, file, indent=4, ensure_ascii=False)


