#!/usr/bin/env python3
"""
Test script for FAISS vector store functionality
"""

import sys
import os

# Add src directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from core.faiss_vector_store import get_faiss_vector_store
from chains.semantic_search_chunk_chain import get_chunk_retriever
from agents.rag_agent import get_agent
from langchain_core.documents import Document

def test_faiss_basic():
    """Test basic FAISS functionality"""
    print("🧪 Testing FAISS Vector Store...")
    
    # Get FAISS store
    faiss_store = get_faiss_vector_store()
    
    # Test adding documents
    test_docs = [
        Document(
            page_content="Đoàn <PERSON>h niên Cộng sản Hồ <PERSON>í <PERSON> là tổ chức chính trị xã hội của thanh niên Việt Nam.",
            metadata={"source": "test", "page": 1}
        ),
        Document(
            page_content="Độ tuổi gia nhập <PERSON><PERSON><PERSON> từ 15 đến 30 tuổi.",
            metadata={"source": "test", "page": 2}
        )
    ]
    
    print(f"📄 Adding {len(test_docs)} test documents...")
    faiss_store.add_documents(test_docs)
    
    # Test search
    query = "độ tuổi gia nhập đoàn"
    print(f"🔍 Searching for: '{query}'")
    
    results = faiss_store.similarity_search(query, k=2)
    print(f"✅ Found {len(results)} results:")
    
    for i, doc in enumerate(results):
        print(f"  {i+1}. {doc.page_content}")
        print(f"     Metadata: {doc.metadata}")
    
    # Get stats
    stats = faiss_store.get_stats()
    print(f"📊 Vector store stats: {stats}")

def test_retriever():
    """Test the retriever functionality"""
    print("\n🧪 Testing Retriever...")
    
    retriever = get_chunk_retriever()
    
    if retriever is None:
        print("❌ Retriever is None")
        return
    
    # Test query
    query = "Đoàn thanh niên"
    print(f"🔍 Testing retriever with query: '{query}'")
    
    try:
        if hasattr(retriever, 'invoke'):
            results = retriever.invoke(query)
        elif hasattr(retriever, 'get_relevant_documents'):
            results = retriever.get_relevant_documents(query)
        else:
            print("❌ Retriever doesn't have expected methods")
            return
        
        print(f"✅ Retriever returned {len(results) if results else 0} results")
        
        if results:
            for i, result in enumerate(results[:2]):  # Show first 2 results
                print(f"  {i+1}. {str(result)[:100]}...")
        
    except Exception as e:
        print(f"❌ Error testing retriever: {e}")

def test_agent():
    """Test the agent functionality"""
    print("\n🧪 Testing Agent...")
    
    try:
        agent = get_agent()
        
        test_input = {
            "input": "Độ tuổi gia nhập đoàn là bao nhiêu?",
            "chat_history": []
        }
        
        print(f"🤖 Testing agent with input: '{test_input['input']}'")
        
        # This is just a basic test to see if agent is created
        print("✅ Agent created successfully")
        print("ℹ️  To fully test the agent, run it with a proper session_id")
        
    except Exception as e:
        print(f"❌ Error testing agent: {e}")

def run_interactive_test():
    """Run an interactive test session"""
    print("\n🎮 Interactive Test Mode")
    print("Type 'quit' to exit")
    
    faiss_store = get_faiss_vector_store()
    
    while True:
        query = input("\nEnter your query: ").strip()
        
        if query.lower() in ['quit', 'exit', 'q']:
            break
        
        if not query:
            continue
        
        try:
            results = faiss_store.similarity_search(query, k=3)
            
            if results:
                print(f"\n✅ Found {len(results)} results:")
                for i, doc in enumerate(results):
                    print(f"\n{i+1}. {doc.page_content}")
                    if doc.metadata:
                        print(f"   Source: {doc.metadata}")
            else:
                print("❌ No results found")
                
        except Exception as e:
            print(f"❌ Error: {e}")

if __name__ == "__main__":
    print("🚀 Starting FAISS Vector Store Tests\n")
    
    # Run basic tests
    test_faiss_basic()
    test_retriever()
    test_agent()
    
    # Ask if user wants interactive mode
    if input("\nRun interactive test? (y/n): ").lower().startswith('y'):
        run_interactive_test()
    
    print("\n🎉 Tests completed!")
