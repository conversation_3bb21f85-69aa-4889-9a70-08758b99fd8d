from langchain_openai import OpenAIEmbeddings
from src.config.settings import settings
from src.core.custom_embeddings import EmbeddingManager as CustomEmbeddingManager, VietnameseEmbeddingService

class EmbeddingManager:
    """
    EmbeddingManager that can use either Vietnamese Embedding Service or OpenAI embeddings
    based on configuration.
    """
    
    def __init__(self):
        if settings.USE_VIETNAMESE_EMBEDDING:
            # Use Vietnamese Embedding Service
            self.embeddings = VietnameseEmbeddingService(
                embedding_service_url=settings.VIETNAMESE_EMBEDDING_SERVICE_URL,
                timeout=settings.EMBEDDING_SERVICE_TIMEOUT,
                normalize=True
            )
            print(f"✅ Using Vietnamese Embedding Service at {settings.VIETNAMESE_EMBEDDING_SERVICE_URL}")
            
            # Test the service connection
            try:
                # Test with a simple Vietnamese text
                test_result = self.embeddings.embed_query("test")
                if test_result:
                    print("✅ Vietnamese Embedding Service connection verified")
                else:
                    print("⚠️ Vietnamese Embedding Service test returned empty result")
            except Exception as e:
                print(f"⚠️ Vietnamese Embedding Service test failed: {e}")
                print("Falling back to OpenAI embeddings...")
                self.embeddings = OpenAIEmbeddings(
                    api_key=settings.OPENAI_API_KEY,
                    model=settings.EMBEDDING_MODEL
                )
        else:
            # Fallback to OpenAI embeddings
            self.embeddings = OpenAIEmbeddings(
                api_key=settings.OPENAI_API_KEY,
                model=settings.EMBEDDING_MODEL
            )
            print(f"✅ Using OpenAI Embeddings with model: {settings.EMBEDDING_MODEL}")
    
    async def get_embeddings(self, texts: list[str]):
        """
        Get embeddings for a list of texts with improved error handling.
        
        Args:
            texts: List of texts to embed
            
        Returns:
            List of embedding vectors
        """
        if not texts:
            return []
        
        # Filter out empty texts
        filtered_texts = [text.strip() for text in texts if text and text.strip()]
        if not filtered_texts:
            raise ValueError("All provided texts are empty")
        
        try:
            return await self.embeddings.aembed_documents(filtered_texts)
        except Exception as e:
            print(f"❌ Embedding error: {e}")
            # If using Vietnamese service and it fails, try fallback to OpenAI
            if (settings.USE_VIETNAMESE_EMBEDDING and
                settings.OPENAI_API_KEY and
                not isinstance(self.embeddings, OpenAIEmbeddings)):
                print("🔄 Attempting fallback to OpenAI embeddings...")
                try:
                    # Switch to OpenAI embeddings for future calls
                    self.embeddings = OpenAIEmbeddings(
                        api_key=settings.OPENAI_API_KEY,
                        model=settings.EMBEDDING_MODEL
                    )
                    return await self.embeddings.aembed_documents(filtered_texts)
                except Exception as fallback_error:
                    print(f"❌ Fallback embedding also failed: {fallback_error}")
            raise e