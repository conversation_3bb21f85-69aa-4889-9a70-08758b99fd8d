from typing import Any, Dict, List, Optional
from dataclasses import dataclass, field
from time import time
from datetime import datetime
from .token_utils import estimate_cost_usd
from .usage_storage import log_usage_record


@dataclass
class UsageRecord:
    query_id: str
    user_id: str
    session_id: str
    model_name: str
    start_time: float = field(default_factory=time)
    query_tokens_before: int = 0
    query_tokens_after: int = 0
    retrieved_docs: List[Dict[str, Any]] = field(default_factory=list)
    retrieved_tokens_total: int = 0
    context_tokens_after_filter: int = 0
    prompt_template_id: Optional[str] = None
    prompt_tokens_estimate: int = 0
    llm_input_tokens_estimate: int = 0
    llm_output_tokens_estimate: int = 0
    llm_latency_ms: Optional[int] = None
    llm_status: Optional[str] = None
    postproc_additional_tokens: int = 0

    def to_summary(self) -> Dict[str, Any]:
        total_input = self.llm_input_tokens_estimate or (
            self.query_tokens_after + self.context_tokens_after_filter + self.prompt_tokens_estimate
        )
        total_output = self.llm_output_tokens_estimate
        cost = estimate_cost_usd(self.model_name, total_input, total_output)
        return {
            "query_id": self.query_id,
            "user_id": self.user_id,
            "session_id": self.session_id,
            "model": self.model_name,
            "query_tokens_before": self.query_tokens_before,
            "query_tokens_after": self.query_tokens_after,
            "retrieved_tokens_total": self.retrieved_tokens_total,
            "context_tokens_after_filter": self.context_tokens_after_filter,
            "prompt_tokens_estimate": self.prompt_tokens_estimate,
            "input_tokens": total_input,
            "output_tokens": total_output,
            "total_tokens": total_input + total_output,
            "cost_usd_estimate": cost,
            "llm_latency_ms": self.llm_latency_ms,
            "llm_status": self.llm_status,
            "retrieved_docs": self.retrieved_docs[:10],  # cap for logging
        }


_STORE: Dict[str, UsageRecord] = {}


def start_query(query_id: str, user_id: str, session_id: str, model_name: str) -> None:
    _STORE[query_id] = UsageRecord(query_id=query_id, user_id=user_id, session_id=session_id, model_name=model_name)


def get(query_id: str) -> Optional[UsageRecord]:
    return _STORE.get(query_id)


def set_query_tokens(query_id: str, before: int, after: int) -> None:
    rec = get(query_id)
    if rec:
        rec.query_tokens_before = before
        rec.query_tokens_after = after


def add_retrieval(query_id: str, docs: List[Dict[str, Any]], tokens_total: int) -> None:
    rec = get(query_id)
    if rec:
        rec.retrieved_docs = docs
        rec.retrieved_tokens_total = tokens_total


def set_context_after_filter(query_id: str, tokens_total: int) -> None:
    rec = get(query_id)
    if rec:
        rec.context_tokens_after_filter = tokens_total


def set_prompt_stats(query_id: str, template_id: str, prompt_tokens: int) -> None:
    rec = get(query_id)
    if rec:
        rec.prompt_template_id = template_id
        rec.prompt_tokens_estimate = prompt_tokens


def set_llm_usage(query_id: str, input_tokens: int, output_tokens: int, latency_ms: int, status: str) -> None:
    rec = get(query_id)
    if rec:
        rec.llm_input_tokens_estimate = input_tokens
        rec.llm_output_tokens_estimate = output_tokens
        rec.llm_latency_ms = latency_ms
        rec.llm_status = status


def add_postproc_tokens(query_id: str, additional_tokens: int) -> None:
    rec = get(query_id)
    if rec:
        rec.postproc_additional_tokens += additional_tokens


def pop_summary(query_id: str) -> Dict[str, Any]:
    rec = _STORE.pop(query_id, None)
    if not rec:
        return {}
    summary = rec.to_summary()
    # Persist condensed usage record as per schema
    ts = datetime.utcnow().isoformat() + "Z"
    record = {
        "timestamp": ts,
        "user_id": rec.user_id,
        "session_id": rec.session_id,
        "query_id": rec.query_id,
        "model": rec.model_name,
        "input_tokens": summary.get("input_tokens", 0),
        "output_tokens": summary.get("output_tokens", 0),
        "total_tokens": summary.get("total_tokens", 0),
        "cost_usd": round(summary.get("cost_usd_estimate", 0.0), 6),
        "status": rec.llm_status or "unknown",
        "latency_ms": rec.llm_latency_ms or 0,
    }
    log_usage_record(record)
    return summary

