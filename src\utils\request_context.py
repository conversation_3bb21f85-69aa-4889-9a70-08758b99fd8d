import contextvars
from typing import Optional

_query_id: contextvars.ContextVar[str] = contextvars.ContextVar("query_id", default="")
_user_id: contextvars.ContextVar[str] = contextvars.ContextVar("user_id", default="")
_session_id: contextvars.ContextVar[str] = contextvars.ContextVar("session_id", default="")


def set_context(query_id: str, user_id: str, session_id: str) -> None:
    _query_id.set(query_id)
    _user_id.set(user_id)
    _session_id.set(session_id)


def get_query_id() -> str:
    return _query_id.get()


def get_user_id() -> str:
    return _user_id.get()


def get_session_id() -> str:
    return _session_id.get()

