import hashlib
import math
from typing import Dict, <PERSON>ple
from config.settings import settings

# Approximate token estimation: avg 4 chars per token
AVG_CHARS_PER_TOKEN = 4.0

# Default limits (can be overridden by cfg.yml via getattr checks)
MODEL_TOKEN_LIMITS: Dict[str, int] = {
    "gpt-4o-mini": 128_000,
}

# Rough pricing in USD per 1K tokens (as of mid-2024; update as needed)
MODEL_PRICING_PER_1K: Dict[str, Tuple[float, float]] = {
    # model: (input_per_1k, output_per_1k)
    "gpt-4o-mini": (0.00015, 0.0006),
}


def estimate_tokens(text: str) -> int:
    if not text:
        return 0
    return max(1, int(math.ceil(len(text) / AVG_CHARS_PER_TOKEN)))


def get_model_token_limit(model_name: str) -> int:
    return MODEL_TOKEN_LIMITS.get(model_name, getattr(settings, "MAX_TOKENS", 4000))


def clamp_tokens(tokens: int, max_tokens: int) -> int:
    return min(tokens, max_tokens)


def estimate_cost_usd(model_name: str, input_tokens: int, output_tokens: int) -> float:
    pricing = MODEL_PRICING_PER_1K.get(model_name)
    if not pricing:
        # Fallback assume $0.001/1k in/out
        in_rate, out_rate = 0.001, 0.001
    else:
        in_rate, out_rate = pricing
    return (input_tokens / 1000.0) * in_rate + (output_tokens / 1000.0) * out_rate


def masked_user_id(raw_id: str) -> str:
    if not raw_id:
        return "anon"
    return hashlib.sha256(raw_id.encode("utf-8")).hexdigest()[:16]

