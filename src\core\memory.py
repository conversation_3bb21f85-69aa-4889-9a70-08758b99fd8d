import os
import json
import pickle
from typing import List, Dict, Any, Optional
from langchain_core.chat_history import BaseChatMessageHistory
from langchain_core.messages import BaseMessage, HumanMessage, AIMessage
from datetime import datetime

class FileChatMessageHistory(BaseChatMessageHistory):
    """
    File-based chat message history to replace Neo4j chat message history
    """
    
    def __init__(self, session_id: str, file_path: Optional[str] = None):
        """
        Initialize FileChatMessageHistory.

        Args:
            session_id: Unique identifier for the chat session
            file_path: Optional custom file path for storing messages

        Raises:
            ValueError: If session_id is empty or contains invalid characters
        """
        if not session_id or not session_id.strip():
            raise ValueError("Session ID cannot be empty")

        # Sanitize session_id to prevent path traversal attacks
        import re
        if not re.match(r'^[a-zA-Z0-9_-]+$', session_id):
            raise ValueError("Session ID can only contain alphanumeric characters, hyphens, and underscores")

        self.session_id = session_id
        if file_path is None:
            # Create a sessions directory if it doesn't exist
            sessions_dir = "chat_sessions"
            os.makedirs(sessions_dir, exist_ok=True)
            self.file_path = os.path.join(sessions_dir, f"{session_id}.json")
        else:
            self.file_path = file_path

        self._messages: List[BaseMessage] = []
        self._load_messages()
    
    def _load_messages(self):
        """Load messages from file"""
        try:
            if os.path.exists(self.file_path):
                with open(self.file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self._messages = []
                    for msg_data in data.get('messages', []):
                        if msg_data['type'] == 'human':
                            self._messages.append(HumanMessage(content=msg_data['content']))
                        elif msg_data['type'] == 'ai':
                            self._messages.append(AIMessage(content=msg_data['content']))
        except (FileNotFoundError, json.JSONDecodeError, KeyError) as e:
            print(f"Warning: Could not load chat history: {e}")
            self._messages = []
        except Exception as e:
            print(f"Unexpected error loading chat history: {e}")
            self._messages = []
    
    def _save_messages(self):
        """Save messages to file"""
        try:
            os.makedirs(os.path.dirname(self.file_path), exist_ok=True)
            data = {
                'session_id': self.session_id,
                'created_at': datetime.now().isoformat(),
                'messages': []
            }
            
            # Use a single timestamp for the save operation
            save_timestamp = datetime.now().isoformat()

            for msg in self._messages:
                if isinstance(msg, HumanMessage):
                    data['messages'].append({
                        'type': 'human',
                        'content': msg.content,
                        'timestamp': save_timestamp
                    })
                elif isinstance(msg, AIMessage):
                    data['messages'].append({
                        'type': 'ai',
                        'content': msg.content,
                        'timestamp': save_timestamp
                    })
            
            with open(self.file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        except (IOError, OSError, json.JSONEncodeError) as e:
            print(f"Warning: Could not save chat history: {e}")
        except Exception as e:
            print(f"Unexpected error saving chat history: {e}")
    
    @property
    def messages(self) -> List[BaseMessage]:
        """Return the list of messages"""
        return self._messages
    
    def add_message(self, message: BaseMessage) -> None:
        """Add a message to the history"""
        self._messages.append(message)
        self._save_messages()
    
    def add_user_message(self, message: str) -> None:
        """Add a user message to the history"""
        self.add_message(HumanMessage(content=message))
    
    def add_ai_message(self, message: str) -> None:
        """Add an AI message to the history"""
        self.add_message(AIMessage(content=message))
    
    def clear(self) -> None:
        """Clear all messages"""
        self._messages = []
        self._save_messages()
    
    def __len__(self) -> int:
        return len(self._messages)
    
    def __iter__(self):
        return iter(self._messages)
    
    def __getitem__(self, index):
        return self._messages[index]


class InMemoryChatMessageHistory(BaseChatMessageHistory):
    """
    Simple in-memory chat message history for temporary sessions
    """
    
    def __init__(self, session_id: str):
        self.session_id = session_id
        self._messages: List[BaseMessage] = []
    
    @property
    def messages(self) -> List[BaseMessage]:
        return self._messages
    
    def add_message(self, message: BaseMessage) -> None:
        self._messages.append(message)
    
    def add_user_message(self, message: str) -> None:
        self.add_message(HumanMessage(content=message))
    
    def add_ai_message(self, message: str) -> None:
        self.add_message(AIMessage(content=message))
    
    def clear(self) -> None:
        self._messages = []
    
    def __len__(self) -> int:
        return len(self._messages)
    
    def __iter__(self):
        return iter(self._messages)
    
    def __getitem__(self, index):
        return self._messages[index]


def get_memory(session_id: str, persist: bool = True) -> BaseChatMessageHistory:
    """
    Get chat message history for a session
    
    Args:
        session_id: The session identifier
        persist: Whether to persist messages to file (True) or keep in memory (False)
    
    Returns:
        Chat message history instance
    """
    if persist:
        return FileChatMessageHistory(session_id)
    else:
        return InMemoryChatMessageHistory(session_id)
