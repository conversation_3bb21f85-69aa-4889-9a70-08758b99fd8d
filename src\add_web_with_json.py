import json
from neo4j_client import Neo4j<PERSON>lient
from utils import chunk_text_html, get_chunk_with_json, get_chunking_config
import asyncio

neo4j = Neo4jClient()

# Function to process a single URL
async def process_link(source, title, chunks,type_document="Website"):
    try:
        print(f"Processing: {source} - {title})")
        filename = title  # Use the title as the filename
        file_id = await neo4j.create_web_with_chunks(filename=filename, chunks=chunks, link=source, type_document=type_document)
        print(f"File created successfully for {source} with ID: {file_id}")
    except Exception as e:
        print(f"Failed to process {source}: {e}")

# Main function to loop through the JSON file
async def main(json_file):
    try:
        # Load the JSON file
        webs = get_chunk_with_json(json_file)

        # Get chunking configuration for JSON content
        chunking_config = get_chunking_config()
        text_splitter = chunking_config.create_text_splitter("json")
        # Loop through each entry in the JSON file
        tasks = []
        for i,data in enumerate(webs[1500:]):
            page_content = data['kwargs']['page_content']
            if len(page_content)>500:
                title = data['kwargs']['metadata']['title']
                source = data['kwargs']['metadata']['source']
                chunks = text_splitter.split_text(page_content)
                print("❌"*10)
                print(i)
                # Process each link asynchronously
                tasks.append(process_link(source, title, chunks))
        
        # Run all tasks concurrently
        await asyncio.gather(*tasks)
        print("All links processed successfully.")
    except Exception as e:
        print(f"Error while processing JSON file: {e}")

    

        # print(chunks)
        # print(title)
        # print(source)

        # process_link(url=source,title=title,chunks=chunks)

# Run the main function
if __name__ == "__main__":
    
    json_file = "/Users/<USER>/Working/thaibinh-chatbot/Crawl_web_data/doanthanhnien.json"  # Replace with your JSON file name

    asyncio.run(main(json_file))