"""
HTML/Web content loading and chunking utilities
Optimized for Vietnamese content with configurable chunking parameters
Now uses trafilatura for robust news content extraction with SSL fallbacks.
"""

import re
import json
import logging
from typing import List, Optional, Tuple, Dict

from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_core.documents import Document
from bs4 import BeautifulSoup

# Optional dependency: trafilatura for robust web extraction
try:
    import trafilatura  # type: ignore
except Exception:  # pragma: no cover - handled at runtime
    trafilatura = None  # will be checked at runtime

# Networking fallbacks
import requests
try:  # certifi for reliable CA bundle
    import certifi
    CERTIFI_PATH = certifi.where()
except Exception:
    CERTIFI_PATH = True  # requests default

from .chunking_config import get_chunking_config

# Configure logging
logger = logging.getLogger(__name__)

def bs4_extractor(html: str) -> str:
    """
    Legacy BeautifulSoup extractor targeting some VN news layouts.
    Kept for backward-compatibility and as a fallback.
    """
    try:
        soup = BeautifulSoup(html, "html.parser")
        # Common VN news containers
        for cls in [
            "news-detail",
            "article-body",
            "content-detail",
            "entry-content",
            "post-content",
        ]:
            target = soup.find(class_=cls)
            if target:
                text = target.get_text(separator=" ", strip=True)
                return re.sub(r"\s+", " ", text)
        # Fallback: whole page text
        text = soup.get_text(separator=" ", strip=True)
        return re.sub(r"\s+", " ", text)
    except Exception as e:
        logger.error(f"BS4 extraction error: {e}")


def _download_html_with_ssl_fallbacks(url: str) -> Tuple[Optional[str], Dict[str, str]]:
    """
    Try multiple strategies to download HTML while handling SSL issues commonly
    seen on some Vietnamese websites.

    Returns:
        (html, meta) where html is the page content or None, and meta contains
        notes about which strategy succeeded.
    """
    meta: Dict[str, str] = {"method": "", "ssl_mode": "verified"}

    # 1) Try trafilatura’s downloader first (HTTPS, verified)
    if trafilatura is not None:
        try:
            html = trafilatura.fetch_url(url, no_ssl=False)
            if html:
                meta["method"] = "trafilatura.fetch_url"
                return html, meta
        except Exception as e:
            logger.warning(f"trafilatura.fetch_url failed ({type(e).__name__}): {e}")

        # 2) Try with no_ssl=True (fall back to HTTP for misconfigured sites)
        try:
            html = trafilatura.fetch_url(url, no_ssl=True)
            if html:
                meta.update({"method": "trafilatura.fetch_url", "ssl_mode": "no_ssl"})
                return html, meta
        except Exception as e:
            logger.warning(f"trafilatura.fetch_url(no_ssl=True) failed ({type(e).__name__}): {e}")

    # 3) Use requests with verified certs (certifi)
    headers = {
        "User-Agent": (
            "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 "
            "(KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36"
        )
    }
    try:
        resp = requests.get(url, headers=headers, timeout=20, verify=CERTIFI_PATH)
        if resp.ok and resp.text:
            meta["method"] = "requests"
            return resp.text, meta
        else:
            logger.warning(f"requests GET returned status={resp.status_code}")
    except requests.exceptions.SSLError as e:
        logger.warning(f"requests verified SSL failed: {e}")
    except Exception as e:
        logger.warning(f"requests failed: {e}")

    # 4) Last resort: requests with verify=False (log and mark as unverified)
    try:
        resp = requests.get(url, headers=headers, timeout=20, verify=False)  # nosec
        if resp.ok and resp.text:
            meta.update({"method": "requests", "ssl_mode": "unverified"})
            logger.warning(
                "SSL verification disabled for %s. Content fetched with verify=False.",
                url,
            )
            return resp.text, meta
    except Exception as e:
        logger.error(f"requests verify=False also failed: {e}")

    # 5) If HTTPS failed, attempt plain HTTP variant as absolute last attempt
    if url.lower().startswith("https://"):
        http_url = "http://" + url[8:]
        try:
            resp = requests.get(http_url, headers=headers, timeout=20, verify=CERTIFI_PATH)
            if resp.ok and resp.text:
                meta.update({"method": "requests", "ssl_mode": "http_fallback"})
                return resp.text, meta
        except Exception as e:
            logger.warning(f"HTTP fallback failed: {e}")

    return None, meta


def _extract_main_content(html: str, url: str) -> Tuple[Optional[str], Dict[str, str]]:
    """
    Extract main content and metadata using trafilatura with sensible fallbacks.
    Returns (text, metadata)
    """
    meta: Dict[str, str] = {"source_url": url}

    if trafilatura is not None:
        try:
            # Prefer JSON to get title/metadata when available
            extracted = trafilatura.extract(
                html,
                url=url,
                with_metadata=True,
                output_format="json",
                include_comments=False,
                favor_precision=True,
            )
            if extracted:
                data = json.loads(extracted)
                text = data.get("text") or ""
                title = data.get("title")
                if title:
                    meta["title"] = title
                return (text.strip() or None), meta
        except Exception as e:
            logger.warning(f"trafilatura.extract JSON failed: {e}")

        # Fallback to plain text
        try:
            text = trafilatura.html2txt(html)
            return (text.strip() or None), meta
        except Exception as e:
            logger.warning(f"trafilatura.html2txt failed: {e}")

    # Final fallback: BeautifulSoup basic extraction
    try:
        text = bs4_extractor(html)
        return (text.strip() or None), meta
    except Exception:
        try:
            soup = BeautifulSoup(html, "html.parser")
            text = soup.get_text(separator=" ", strip=True)
            return (re.sub(r"\s+", " ", text).strip() or None), meta
        except Exception as e:
            logger.error(f"All extraction methods failed: {e}")
            return None, meta


def _build_documents_from_text(text: str, url: str, extra_meta: Dict[str, str]) -> List[Document]:
    doc = Document(page_content=text, metadata={"source": url, **extra_meta})
    return [doc]



def get_content_from_url(url: str, content_type: str = "web", preprocess: bool = True) -> List[Document]:
    """
    Fetch content from a URL using trafilatura with robust SSL handling, then
    split it into chunks according to configuration.
    """
    # Download
    html, dl_meta = _download_html_with_ssl_fallbacks(url)
    if not html:
        raise ValueError("No documents were loaded from the URL.")

    # Extract
    text, meta = _extract_main_content(html, url)
    if not text:
        raise ValueError("Could not extract main content from the URL.")

    # Build Document list
    base_docs = _build_documents_from_text(text, url, {**dl_meta, **meta})

    # Chunking
    chunking = get_chunking_config()
    splitter = chunking.create_text_splitter(content_type)

    docs_for_split = base_docs
    if preprocess:
        processed_docs = []
        for doc in base_docs:
            processed = chunking.preprocess_vietnamese_text(doc.page_content)
            processed_docs.append(Document(page_content=processed, metadata=doc.metadata))
        docs_for_split = processed_docs

    chunks = splitter.split_documents(docs_for_split)
    if not chunks:
        raise ValueError("The document content could not be split into chunks.")

    logger.info(f"Successfully chunked URL {url}: {len(chunks)} chunks created")
    return chunks


def get_content_from_url_with_custom_config(
    url: str,
    chunk_size: int,
    chunk_overlap: int,
    separators: Optional[List[str]] = None,
) -> List[Document]:
    """
    Fetch content from a URL with custom chunking configuration using trafilatura.
    """
    html, dl_meta = _download_html_with_ssl_fallbacks(url)
    if not html:
        raise ValueError("No documents were loaded from the URL.")

    text, meta = _extract_main_content(html, url)
    if not text:
        raise ValueError("Could not extract main content from the URL.")

    base_docs = _build_documents_from_text(text, url, {**dl_meta, **meta})

    chunking = get_chunking_config()
    if separators is None:
        separators = chunking.get_vietnamese_separators()

    splitter = RecursiveCharacterTextSplitter(
        separators=separators,
        chunk_size=chunk_size,
        chunk_overlap=chunk_overlap,
        length_function=len,
        keep_separator=True,
        strip_whitespace=True,
        add_start_index=True,
    )

    chunks = splitter.split_documents(base_docs)
    if not chunks:
        raise ValueError("The document content could not be split into chunks.")

    logger.info(
        f"Successfully chunked URL {url} with custom config: {len(chunks)} chunks created"
    )
    return chunks


def chunk_text_html(text: str, content_type: str = "web") -> List[str]:
    """
    Chunk HTML text content using configuration from cfg.yml

    Args:
        text: HTML text content to chunk
        content_type: Type of content for chunking configuration (default: "web")

    Returns:
        List of text chunks
    """
    try:
        chunking_config = get_chunking_config()
        text_splitter = chunking_config.create_text_splitter(content_type)

        # Preprocess text
        processed_text = chunking_config.preprocess_vietnamese_text(text)

        # Split text into chunks
        chunks = text_splitter.split_text(processed_text)

        logger.info(f"Successfully chunked HTML text: {len(chunks)} chunks created")
        return chunks

    except Exception as e:
        logger.error(f"Error chunking HTML text: {e}")
        return []

# Example usage
if __name__ == "__main__":
    url = "https://doanthanhnien.vn/tin-tuc/cong-tac-giao-duc/gia-lai-nang-cao-ky-nang-nghiep-vu-cho-luc-luong-bao-cao-vien-can-bo-doan-chu-chot"

    try:
        docs = get_content_from_url(url)
        print(docs)
    except Exception as e:
        logging.error(f"An error occurred while processing the URL: {e}")