import os
import json
from datetime import datetime, timedelta
from typing import Dict, Iterator, List, Optional
from config.settings import settings

DATE_FMT = "%Y-%m-%d"


def _ensure_dir(path: str) -> None:
    try:
        os.makedirs(path, exist_ok=True)
    except Exception:
        # Best-effort; callers handle errors
        pass


def _log_dir() -> str:
    d = getattr(settings, "USAGE_LOG_DIR", "usage_logs")
    _ensure_dir(d)
    return d


def _path_for_date(dt: datetime) -> str:
    fname = dt.strftime(DATE_FMT) + ".jsonl"
    return os.path.join(_log_dir(), fname)


def cleanup_old_logs() -> None:
    """Delete log files older than retention days."""
    try:
        retention = int(getattr(settings, "USAGE_LOG_RETENTION_DAYS", 30))
    except Exception:
        retention = 30
    cutoff = datetime.utcnow().date() - timedelta(days=retention)
    d = _log_dir()
    try:
        for name in os.listdir(d):
            if not name.endswith(".jsonl"):
                continue
            try:
                date_part = name.replace(".jsonl", "")
                file_date = datetime.strptime(date_part, DATE_FMT).date()
                if file_date < cutoff:
                    os.remove(os.path.join(d, name))
            except Exception:
                continue
    except Exception:
        # swallow errors; not critical
        pass


def log_usage_record(record: Dict) -> None:
    """Append a usage record (JSON) to today's log file."""
    try:
        # Determine file based on record timestamp if provided
        ts = record.get("timestamp")
        try:
            dt = datetime.fromisoformat(ts.replace("Z", "+00:00")) if ts else datetime.utcnow()
        except Exception:
            dt = datetime.utcnow()
        path = _path_for_date(dt)
        with open(path, "a", encoding="utf-8") as f:
            f.write(json.dumps(record, ensure_ascii=False) + "\n")
    except Exception:
        # Do not raise; avoid breaking request path
        pass
    # Best-effort cleanup
    cleanup_old_logs()


def _daterange(start: datetime, end: datetime) -> List[datetime]:
    days = []
    cur = start
    while cur.date() <= end.date():
        days.append(cur)
        cur += timedelta(days=1)
    return days


def iter_records(
    start: Optional[datetime] = None,
    end: Optional[datetime] = None,
    user_id: Optional[str] = None,
    session_id: Optional[str] = None,
    model: Optional[str] = None,
) -> Iterator[Dict]:
    """Yield records filtered by date range and optional fields."""
    d = _log_dir()
    if start is None:
        start = datetime.utcnow() - timedelta(days=30)
    if end is None:
        end = datetime.utcnow()
    days = _daterange(start, end)
    for day in days:
        path = _path_for_date(day)
        if not os.path.exists(path):
            continue
        try:
            with open(path, "r", encoding="utf-8") as f:
                for line in f:
                    try:
                        rec = json.loads(line)
                    except Exception:
                        continue
                    # Quick date filter
                    ts = rec.get("timestamp")
                    try:
                        rdt = datetime.fromisoformat(ts.replace("Z", "+00:00")) if ts else None
                    except Exception:
                        rdt = None
                    if rdt is None or rdt < start or rdt > end:
                        continue
                    if user_id and rec.get("user_id") != user_id:
                        continue
                    if session_id and rec.get("session_id") != session_id:
                        continue
                    if model and rec.get("model") != model:
                        continue
                    yield rec
        except Exception:
            continue


def aggregate_summary(records: Iterator[Dict], group_by: str = "day") -> Dict:
    """Aggregate records by time period with totals/averages."""
    from collections import defaultdict

    buckets = defaultdict(lambda: {
        "input_tokens": 0,
        "output_tokens": 0,
        "total_tokens": 0,
        "cost_usd": 0.0,
        "latency_ms_sum": 0,
        "count": 0,
        "success": 0,
        "error": 0,
        "timeout": 0,
    })

    def key_for(dt: datetime) -> str:
        if group_by == "year":
            return dt.strftime("%Y")
        elif group_by == "month":
            return dt.strftime("%Y-%m")
        else:
            return dt.strftime(DATE_FMT)

    total = {
        "input_tokens": 0,
        "output_tokens": 0,
        "total_tokens": 0,
        "cost_usd": 0.0,
        "avg_latency_ms": 0.0,
        "count": 0,
    }

    for rec in records:
        ts = rec.get("timestamp")
        try:
            dt = datetime.fromisoformat(ts.replace("Z", "+00:00")) if ts else None
        except Exception:
            dt = None
        if dt is None:
            continue
        k = key_for(dt)
        b = buckets[k]
        b["input_tokens"] += int(rec.get("input_tokens", 0))
        b["output_tokens"] += int(rec.get("output_tokens", 0))
        b["total_tokens"] += int(rec.get("total_tokens", 0))
        b["cost_usd"] += float(rec.get("cost_usd", 0.0))
        b["latency_ms_sum"] += int(rec.get("latency_ms", 0) or 0)
        b["count"] += 1
        st = rec.get("status")
        if st == "success":
            b["success"] += 1
        elif st == "timeout":
            b["timeout"] += 1
        elif st == "error":
            b["error"] += 1

        total["input_tokens"] += int(rec.get("input_tokens", 0))
        total["output_tokens"] += int(rec.get("output_tokens", 0))
        total["total_tokens"] += int(rec.get("total_tokens", 0))
        total["cost_usd"] += float(rec.get("cost_usd", 0.0))
        total["latency_ms"] = total.get("latency_ms", 0) + int(rec.get("latency_ms", 0) or 0)
        total["count"] += 1

    # Compute averages per bucket
    summary = {}
    for k, v in buckets.items():
        count = max(1, v["count"])
        summary[k] = {
            "input_tokens": v["input_tokens"],
            "output_tokens": v["output_tokens"],
            "total_tokens": v["total_tokens"],
            "cost_usd": round(v["cost_usd"], 6),
            "avg_latency_ms": round(v["latency_ms_sum"] / count, 2),
            "count": v["count"],
            "status_counts": {
                "success": v["success"],
                "timeout": v["timeout"],
                "error": v["error"],
            },
        }

    total_count = max(1, total["count"]) if total.get("count") else 1
    total["avg_latency_ms"] = round((total.get("latency_ms", 0) or 0) / total_count, 2)
    if "latency_ms" in total:
        del total["latency_ms"]

    return {"buckets": summary, "total": total}


def cost_breakdown(records: Iterator[Dict]) -> Dict:
    from collections import defaultdict

    total_cost = 0.0
    by_model = defaultdict(float)
    by_user = defaultdict(float)

    for rec in records:
        c = float(rec.get("cost_usd", 0.0))
        total_cost += c
        by_model[rec.get("model", "unknown")] += c
        by_user[rec.get("user_id", "unknown")] += c

    return {
        "total_cost_usd": round(total_cost, 6),
        "by_model": {k: round(v, 6) for k, v in by_model.items()},
        "by_user": {k: round(v, 6) for k, v in by_user.items()},
    }

