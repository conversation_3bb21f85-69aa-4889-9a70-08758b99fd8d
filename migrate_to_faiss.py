#!/usr/bin/env python3
"""
Migration script to convert data from Neo4j to FAISS vector store
This script helps migrate existing data from Neo4j graph database to FAISS vector database
"""

import os
import json
from typing import List, Dict, Any
from langchain_core.documents import Document
from core.faiss_vector_store import get_faiss_vector_store

def create_sample_documents():
    """
    Create sample documents for testing FAISS vector store
    Replace this with your actual data migration logic
    """
    sample_docs = [
        {
            "content": "Đoàn Thanh niên Cộng sản Hồ <PERSON> là tổ chức chính trị - xã hội của thanh niên Vi<PERSON>t Nam, đ<PERSON><PERSON><PERSON> thành lập theo chủ nghĩa Mác - Lênin và tư tưởng Hồ <PERSON>.",
            "metadata": {
                "filename": "dieu_le_doan.pdf",
                "page_number": 1,
                "link": "https://example.com/dieu_le_doan.pdf",
                "chunk_id": "chunk_1"
            }
        },
        {
            "content": "<PERSON><PERSON> tuổi gia nhập <PERSON><PERSON><PERSON><PERSON> 15 tuổi đến 30 tuổi. <PERSON>h<PERSON><PERSON> người từ 15 đến dưới 18 tuổi gia nhập Đoàn phải có sự đồng ý của cha mẹ hoặc người giám hộ.",
            "metadata": {
                "filename": "dieu_le_doan.pdf", 
                "page_number": 5,
                "link": "https://example.com/dieu_le_doan.pdf",
                "chunk_id": "chunk_2"
            }
        },
        {
            "content": "Nhiệm vụ của Đoàn Thanh niên Cộng sản Hồ Chí Minh là tập hợp, đoàn kết, giáo dục thanh niên Việt Nam theo mục tiêu, lý tưởng cách mạng của Đảng.",
            "metadata": {
                "filename": "dieu_le_doan.pdf",
                "page_number": 3,
                "link": "https://example.com/dieu_le_doan.pdf", 
                "chunk_id": "chunk_3"
            }
        },
        {
            "content": "Cơ cấu tổ chức của Đoàn gồm: Chi đoàn, Đoàn cơ sở, Đoàn trường, Đoàn cấp huyện, Đoàn cấp tỉnh và Trung ương Đoàn.",
            "metadata": {
                "filename": "dieu_le_doan.pdf",
                "page_number": 8,
                "link": "https://example.com/dieu_le_doan.pdf",
                "chunk_id": "chunk_4"
            }
        },
        {
            "content": "Quyền của đoàn viên bao gồm: được thảo luận, phát biểu ý kiến về chủ trương, chính sách của Đảng, pháp luật của Nhà nước và các vấn đề của Đoàn.",
            "metadata": {
                "filename": "dieu_le_doan.pdf",
                "page_number": 12,
                "link": "https://example.com/dieu_le_doan.pdf",
                "chunk_id": "chunk_5"
            }
        }
    ]
    
    # Convert to Document objects
    documents = []
    for doc_data in sample_docs:
        doc = Document(
            page_content=doc_data["content"],
            metadata=doc_data["metadata"]
        )
        documents.append(doc)
    
    return documents

def migrate_to_faiss():
    """
    Main migration function
    """
    print("🚀 Starting migration to FAISS vector store...")
    
    # Get FAISS vector store
    faiss_store = get_faiss_vector_store()
    
    # Clear existing data (optional)
    # faiss_store.delete_vector_store()
    
    # Create sample documents (replace with your actual data loading logic)
    documents = create_sample_documents()
    
    print(f"📄 Adding {len(documents)} documents to FAISS vector store...")
    
    # Add documents to FAISS
    faiss_store.add_documents(documents)
    
    # Get stats
    stats = faiss_store.get_stats()
    print(f"✅ Migration completed successfully!")
    print(f"📊 Vector store statistics: {stats}")
    
    # Test the vector store
    print("\n🧪 Testing vector store...")
    test_query = "độ tuổi gia nhập đoàn"
    results = faiss_store.similarity_search(test_query, k=2)
    
    print(f"Test query: '{test_query}'")
    print(f"Results found: {len(results)}")
    for i, result in enumerate(results):
        print(f"Result {i+1}: {result.page_content[:100]}...")
        print(f"Metadata: {result.metadata}")
        print()

def load_from_json_file(file_path: str):
    """
    Load documents from a JSON file
    Expected format:
    [
        {
            "content": "text content",
            "metadata": {"filename": "...", "page_number": 1, ...}
        },
        ...
    ]
    """
    print(f"📂 Loading documents from {file_path}...")
    
    if not os.path.exists(file_path):
        print(f"❌ File not found: {file_path}")
        return []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        documents = []
        for item in data:
            doc = Document(
                page_content=item.get("content", ""),
                metadata=item.get("metadata", {})
            )
            documents.append(doc)
        
        print(f"✅ Loaded {len(documents)} documents from JSON file")
        return documents
        
    except Exception as e:
        print(f"❌ Error loading JSON file: {e}")
        return []

def migrate_from_json(json_file_path: str):
    """
    Migrate data from a JSON file to FAISS
    """
    print("🚀 Starting migration from JSON to FAISS...")
    
    # Load documents from JSON
    documents = load_from_json_file(json_file_path)
    
    if not documents:
        print("❌ No documents to migrate")
        return
    
    # Get FAISS vector store
    faiss_store = get_faiss_vector_store()
    
    # Add documents
    faiss_store.add_documents(documents)
    
    # Get stats
    stats = faiss_store.get_stats()
    print(f"✅ Migration completed successfully!")
    print(f"📊 Vector store statistics: {stats}")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        # If JSON file path is provided
        json_file = sys.argv[1]
        migrate_from_json(json_file)
    else:
        # Use sample data
        migrate_to_faiss()
    
    print("\n🎉 Migration script completed!")
