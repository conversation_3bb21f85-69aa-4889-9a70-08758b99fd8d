import os
from typing import List, Dict, Optional
from langchain_community.vectorstores import FAISS
from langchain_core.documents import Document
from src.core.embeddings import EmbeddingManager

class VectorStoreManager:
    """
    Manages FAISS vector store operations with proper async/sync handling.

    This class provides a clean interface for vector store operations including
    initialization, document addition, similarity search, and persistence.
    """

    def __init__(self, embedding_manager: EmbeddingManager, persist_directory: str = "faiss_index"):
        """
        Initialize the VectorStoreManager.

        Args:
            embedding_manager: The embedding manager instance
            persist_directory: Directory to persist the vector store
        """
        self.embedding_manager = embedding_manager
        self.persist_directory = persist_directory
        self.vector_store = None

        # Create directory if it doesn't exist
        os.makedirs(persist_directory, exist_ok=True)
    
    async def init_store(self, documents: List[Document]):
        """Initialize FAISS store with documents"""
        if not documents:
            return
            
        # Extract texts and metadata
        texts = [doc.page_content for doc in documents]
        metadatas = [doc.metadata for doc in documents]
        
        # Create FAISS vector store
        self.vector_store = await FAISS.afrom_texts(
            texts=texts,
            embedding=self.embedding_manager.embeddings,
            metadatas=metadatas
        )
        
        # Save the vector store
        await self.save_vector_store()
    
    async def add_documents(self, documents: List[Document]):
        """Add new documents to existing vector store"""
        if not self.vector_store:
            await self.init_store(documents)
            return
            
        texts = [doc.page_content for doc in documents]
        metadatas = [doc.metadata for doc in documents]
        
        await self.vector_store.aadd_texts(texts=texts, metadatas=metadatas)
        await self.save_vector_store()
    
    async def similarity_search(self, query: str, k: int = 3, score_threshold: float = 0.0):
        """
        Perform similarity search on the vector store.

        Args:
            query: Search query string
            k: Number of results to return
            score_threshold: Minimum similarity score threshold (lower scores are better)

        Returns:
            List of documents matching the query
        """
        if not self.vector_store:
            await self.load_vector_store()

        if not self.vector_store:
            return []

        if score_threshold > 0:
            results = await self.vector_store.asimilarity_search_with_score(query, k=k)
            # FAISS uses distance metrics where lower scores are better
            return [doc for doc, score in results if score <= score_threshold]
        else:
            return await self.vector_store.asimilarity_search(query, k=k)
    
    async def save_vector_store(self):
        """Save FAISS vector store to disk"""
        if self.vector_store:
            self.vector_store.save_local(self.persist_directory)
    
    async def load_vector_store(self):
        """Load FAISS vector store from disk"""
        index_path = os.path.join(self.persist_directory, "index.faiss")
        if os.path.exists(index_path):
            self.vector_store = FAISS.load_local(
                self.persist_directory, 
                self.embedding_manager.embeddings,
                allow_dangerous_deserialization=True
            )
    
    def as_retriever(self, search_type: str = "similarity", search_kwargs: Optional[Dict] = None):
        """
        Return a retriever interface for the vector store.

        Args:
            search_type: Type of search to perform
            search_kwargs: Additional search parameters

        Returns:
            Retriever interface or None if vector store not available
        """
        if search_kwargs is None:
            search_kwargs = {"k": 3}

        if not self.vector_store:
            # Cannot load asynchronously in sync method
            # Caller should ensure vector store is loaded
            return None

        return self.vector_store.as_retriever(
            search_type=search_type,
            search_kwargs=search_kwargs
        )