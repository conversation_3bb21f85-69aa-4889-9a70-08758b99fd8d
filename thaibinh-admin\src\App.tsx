import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { Page } from '@patternfly/react-core';
import AdminLayout from './layouts/AdminLayout';
import DocumentManagement from './pages/DocumentManagement';
import ChatbotManagement from './pages/ChatbotManagement';

const App: React.FC = () => {
  return (
    <Router>
      <Page>
        <AdminLayout>
          <Routes>
            <Route path="/" element={<DocumentManagement />} />
            <Route path="/documents" element={<DocumentManagement />} />
            <Route path="/chatbot" element={<ChatbotManagement />} />
          </Routes>
        </AdminLayout>
      </Page>
    </Router>
  );
};

export default App; 