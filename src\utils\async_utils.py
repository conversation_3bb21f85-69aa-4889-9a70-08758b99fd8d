"""
Async utilities for Thai Binh Chatbot
Provides retry mechanisms and other async helpers with configurable parameters
"""

import asyncio
import logging
from typing import Callable, Any, Optional
from config.settings import settings

# Configure logging using settings
logger = logging.getLogger(__name__)


def async_retry1(max_retries: Optional[int] = None, delay: Optional[int] = None):
    """
    Async retry decorator with configurable parameters

    Args:
        max_retries: Maximum number of retry attempts (default from settings or 3)
        delay: Delay between retries in seconds (default 1)

    Returns:
        Decorated function with retry capability
    """
    # Use default values if not provided
    if max_retries is None:
        max_retries = getattr(settings, 'ASYNC_MAX_RETRIES', 3)
    if delay is None:
        delay = getattr(settings, 'ASYNC_RETRY_DELAY', 1)

    def decorator(func: Callable) -> Callable:
        async def wrapper(*args, **kwargs) -> Any:
            last_exception = None

            for attempt in range(1, max_retries + 1):
                try:
                    result = await func(*args, **kwargs)
                    if attempt > 1:
                        logger.info(f"Function '{func.__name__}' succeeded on attempt {attempt}")
                    return result
                except Exception as e:
                    last_exception = e
                    logger.warning(f"Attempt {attempt}/{max_retries} failed for '{func.__name__}': {str(e)}")

                    if attempt < max_retries:
                        await asyncio.sleep(delay)

            logger.error(f"All {max_retries} attempts failed for '{func.__name__}'. "
                        f"Last error: {str(last_exception)}")
            return None

        return wrapper

    return decorator


def async_timeout(timeout_seconds: Optional[float] = None):
    """
    Async timeout decorator

    Args:
        timeout_seconds: Timeout in seconds (default from settings or 30)

    Returns:
        Decorated function with timeout capability
    """
    if timeout_seconds is None:
        timeout_seconds = getattr(settings, 'ASYNC_TIMEOUT', 30.0)

    def decorator(func: Callable) -> Callable:
        async def wrapper(*args, **kwargs) -> Any:
            try:
                return await asyncio.wait_for(func(*args, **kwargs), timeout=timeout_seconds)
            except asyncio.TimeoutError:
                logger.error(f"Function '{func.__name__}' timed out after {timeout_seconds} seconds")
                return None
            except Exception as e:
                logger.error(f"Function '{func.__name__}' failed: {str(e)}")
                return None

        return wrapper

    return decorator


def async_retry_with_timeout(max_retries: Optional[int] = None,
                           delay: Optional[int] = None,
                           timeout_seconds: Optional[float] = None):
    """
    Combined retry and timeout decorator

    Args:
        max_retries: Maximum number of retry attempts
        delay: Delay between retries in seconds
        timeout_seconds: Timeout for each attempt in seconds

    Returns:
        Decorated function with both retry and timeout capability
    """
    def decorator(func: Callable) -> Callable:
        # Apply timeout first, then retry
        timeout_func = async_timeout(timeout_seconds)(func)
        retry_func = async_retry1(max_retries, delay)(timeout_func)
        return retry_func

    return decorator
