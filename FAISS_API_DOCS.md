# FAISS-Based File Management API Documentation

## Tổng quan

Sau khi chuyển từ Neo4j sang FAISS, hệ thống đã được thiết kế lại để cung cấp các API tương thích với cấu trúc file/chunk sử dụng FAISS vector store.

## API Endpoints

### 1. 📂 Quản lý Files

#### GET `/files`
Liệt kê tất cả files đã upload

**Response:**
```json
{
  "files": [
    {
      "file_id": "uuid-string",
      "filename": "document.pdf",
      "link": "https://example.com",
      "description": "Sample document",
      "tags": ["tag1", "tag2"],
      "type": "File",
      "source": "upload",
      "chunk_count": 5
    }
  ],
  "total": 1
}
```

#### GET `/files/{file_id}/chunks`
Lấy tất cả chunks của một file

**Parameters:**
- `file_id`: UUID của file (upload_id)

**Response:**
```json
{
  "file_id": "uuid-string",
  "chunks": [
    {
      "chunk_id": "document.pdf_chunk_0",
      "chunk_index": 0,
      "content": "Content of the first chunk...",
      "metadata": {
        "filename": "document.pdf",
        "upload_id": "uuid-string",
        "chunk_index": 0
      }
    }
  ],
  "total_chunks": 5
}
```

#### GET `/files/{file_id}/chunks/{chunk_id}`
Lấy thông tin chi tiết của một chunk

**Parameters:**
- `file_id`: UUID của file
- `chunk_id`: ID của chunk

**Response:**
```json
{
  "file_id": "uuid-string",
  "chunk_id": "document.pdf_chunk_0",
  "content": "Full content of the chunk...",
  "metadata": {
    "filename": "document.pdf",
    "upload_id": "uuid-string",
    "chunk_index": 0,
    "link": "https://example.com"
  }
}
```

#### PUT `/files/{file_id}/chunks/{chunk_id}`
Cập nhật nội dung của một chunk

**Parameters:**
- `file_id`: UUID của file
- `chunk_id`: ID của chunk

**Request Body:**
```json
"New content for the chunk"
```

**Response:**
```json
{
  "message": "Chunk updated successfully",
  "file_id": "uuid-string",
  "chunk_id": "document.pdf_chunk_0",
  "new_content": "New content for the chunk"
}
```

**⚠️ Lưu ý:** Việc cập nhật chunk sẽ tái tạo toàn bộ vector store, có thể chậm với datasets lớn.

#### DELETE `/files/{file_id}`
Xóa một file và tất cả chunks của nó

**Parameters:**
- `file_id`: UUID của file

**Response:**
```json
{
  "message": "File uuid-string deleted successfully",
  "deleted_chunks": 5,
  "remaining_documents": 20
}
```

**⚠️ Lưu ý:** Việc xóa file sẽ tái tạo toàn bộ vector store.

### 2. 🔍 Tìm kiếm

#### POST `/search`
Tìm kiếm documents trong vector store

**Request Body:**
```json
{
  "query": "Đoàn thanh niên",
  "k": 3
}
```

**Response:**
```json
{
  "query": "Đoàn thanh niên",
  "results": [
    {
      "content": "Content of matching document...",
      "metadata": {
        "filename": "document.pdf",
        "upload_id": "uuid-string"
      }
    }
  ],
  "count": 1
}
```

### 3. 📊 Thống kê

#### GET `/stats`
Lấy thống kê về vector store

**Response:**
```json
{
  "total_documents": 25,
  "embedding_dimension": 1536,
  "vector_store_type": "FAISS"
}
```

## Khác biệt so với Neo4j

| Aspect | Neo4j | FAISS |
|--------|-------|-------|
| File ID | Neo4j node ID | UUID (upload_id) |
| Chunk ID | Neo4j node ID | Generated string |
| Relationships | Native graph relationships | Metadata-based |
| Updates | Direct node updates | Full rebuild required |
| Deletes | Direct node deletion | Full rebuild required |
| Search | Cypher queries + vector | Vector similarity only |

## Hiệu năng

### Ưu điểm
- **Tìm kiếm nhanh:** FAISS được tối ưu cho similarity search
- **Không cần database:** Chỉ cần file system
- **Memory efficient:** Tùy chọn lưu trữ trên disk

### Nhược điểm
- **Updates chậm:** Phải rebuild toàn bộ index
- **Deletes chậm:** Phải rebuild toàn bộ index
- **Không có relationships:** Mất khả năng query phức tạp

## Best Practices

### 1. Batch Operations
```python
# Tốt: Upload nhiều files cùng lúc
documents = [doc1, doc2, doc3]
faiss_store.add_documents(documents)

# Không tốt: Upload từng file một
for doc in documents:
    faiss_store.add_documents([doc])
```

### 2. Minimize Updates/Deletes
- Tránh update/delete thường xuyên
- Group multiple changes together
- Consider re-uploading entire documents instead of editing chunks

### 3. Monitoring
```python
# Check stats regularly
stats = faiss_store.get_stats()
print(f"Total docs: {stats['total_documents']}")
```

## Error Handling

### Common Errors

1. **404 File Not Found**
   ```json
   {"detail": "File not found"}
   ```

2. **404 Chunk Not Found**
   ```json
   {"detail": "Chunk not found"}
   ```

3. **500 Vector Store Error**
   ```json
   {"detail": "Error in FAISS operation"}
   ```

## Testing

### Unit Tests
```bash
python test_file_apis.py
```

### Manual Testing
```bash
# List files
curl -X GET "http://localhost:8000/files"

# Get file chunks
curl -X GET "http://localhost:8000/files/{file_id}/chunks"

# Search
curl -X POST "http://localhost:8000/search" \
  -H "Content-Type: application/json" \
  -d '{"query": "search term", "k": 3}'
```

## Migration Guide

### From Neo4j to FAISS

1. **Export data from Neo4j:**
   ```cypher
   MATCH (f:File)-[:HAS_CHUNK]->(c:Chunk)
   RETURN f.filename, f.link, c.text, c.page_number
   ```

2. **Convert to FAISS format:**
   ```python
   documents = []
   for record in neo4j_data:
       doc = Document(
           page_content=record['c.text'],
           metadata={
               'filename': record['f.filename'],
               'link': record['f.link'],
               'upload_id': str(uuid.uuid4())
           }
       )
       documents.append(doc)
   ```

3. **Load into FAISS:**
   ```python
   faiss_store.add_documents(documents)
   ```

## Troubleshooting

### Common Issues

1. **Empty results from get_all_documents()**
   - Check if vector store is properly loaded
   - Verify FAISS index files exist

2. **Slow performance on updates/deletes**
   - Expected behavior with FAISS
   - Consider batching operations

3. **Memory issues**
   - Monitor vector store size
   - Consider using FAISS with disk storage

### Debug Commands

```bash
# Check FAISS files
ls -la faiss_index/

# Check API logs
tail -f api.log

# Test vector store directly
python -c "from core.faiss_vector_store import get_faiss_vector_store; print(get_faiss_vector_store().get_stats())"
```
