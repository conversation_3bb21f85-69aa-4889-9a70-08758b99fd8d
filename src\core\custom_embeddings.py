"""
Custom embedding implementation using the Vietnamese Embedding Service API
"""

import asyncio
import aiohttp
import requests
from typing import List, Optional
from langchain_core.embeddings import Embeddings
from config.settings import settings


class VietnameseEmbeddingService(Embeddings):
    """
    Custom embedding class that uses the Vietnamese Embedding Service API
    instead of OpenAI embeddings.
    """
    
    def __init__(
        self,
        embedding_service_url: str = "http://localhost:2603",
        timeout: int = 30,
        normalize: bool = True
    ):
        """
        Initialize the Vietnamese Embedding Service client.
        
        Args:
            embedding_service_url: URL of the embedding service
            timeout: Request timeout in seconds
            normalize: Whether to normalize embeddings
        """
        self.embedding_service_url = embedding_service_url.rstrip('/')
        self.timeout = timeout
        self.normalize = normalize
        self.endpoint = f"{self.embedding_service_url}/api/v1/embedding"
    
    def embed_documents(self, texts: List[str]) -> List[List[float]]:
        """
        Embed a list of documents using the Vietnamese Embedding Service.
        
        Args:
            texts: List of texts to embed
            
        Returns:
            List of embeddings
        """
        if not texts:
            return []
        
        # Filter out empty texts
        filtered_texts = [text.strip() for text in texts if text and text.strip()]
        if not filtered_texts:
            raise ValueError("All provided texts are empty")
        
        payload = {
            "texts": filtered_texts,
            "normalize": self.normalize
        }
        
        try:
            response = requests.post(
                self.endpoint,
                json=payload,
                timeout=self.timeout,
                headers={"Content-Type": "application/json"}
            )
            response.raise_for_status()
            
            result = response.json()
            
            if result.get("is_success", False):
                embeddings = result.get("embeddings", [])
                if not embeddings:
                    raise Exception("Embedding service returned empty embeddings")
                if len(embeddings) != len(filtered_texts):
                    raise Exception(f"Expected {len(filtered_texts)} embeddings, got {len(embeddings)}")
                return embeddings
            else:
                raise Exception(f"Embedding service error: {result.get('msg', 'Unknown error')}")
                
        except requests.exceptions.RequestException as e:
            raise Exception(f"Failed to get embeddings from service: {str(e)}")
    
    def embed_query(self, text: str) -> List[float]:
        """
        Embed a single query text.
        
        Args:
            text: Text to embed
            
        Returns:
            Embedding vector
        """
        if not text or not text.strip():
            raise ValueError("Empty text provided for embedding")
            
        result = self.embed_documents([text])
        if not result or len(result) == 0:
            raise Exception("No embeddings returned from service")
        return result[0]
    
    async def aembed_documents(self, texts: List[str]) -> List[List[float]]:
        """
        Asynchronously embed a list of documents.
        
        Args:
            texts: List of texts to embed
            
        Returns:
            List of embeddings
        """
        if not texts:
            return []
        
        # Filter out empty texts
        filtered_texts = [text.strip() for text in texts if text and text.strip()]
        if not filtered_texts:
            raise ValueError("All provided texts are empty")
        
        payload = {
            "texts": filtered_texts,
            "normalize": self.normalize
        }
        
        try:
            timeout = aiohttp.ClientTimeout(total=self.timeout)
            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.post(
                    self.endpoint,
                    json=payload,
                    headers={"Content-Type": "application/json"}
                ) as response:
                    response.raise_for_status()
                    result = await response.json()
                    
                    if result.get("is_success", False):
                        embeddings = result.get("embeddings", [])
                        if not embeddings:
                            raise Exception("Embedding service returned empty embeddings")
                        if len(embeddings) != len(filtered_texts):
                            raise Exception(f"Expected {len(filtered_texts)} embeddings, got {len(embeddings)}")
                        return embeddings
                    else:
                        raise Exception(f"Embedding service error: {result.get('msg', 'Unknown error')}")
                        
        except aiohttp.ClientError as e:
            raise Exception(f"Failed to get embeddings from service: {str(e)}")
    
    async def aembed_query(self, text: str) -> List[float]:
        """
        Asynchronously embed a single query text.
        
        Args:
            text: Text to embed
            
        Returns:
            Embedding vector
        """
        if not text or not text.strip():
            raise ValueError("Empty text provided for embedding")
            
        result = await self.aembed_documents([text])
        if not result or len(result) == 0:
            raise Exception("No embeddings returned from service")
        return result[0]


class EmbeddingManager:
    """
    Updated EmbeddingManager that uses Vietnamese Embedding Service
    instead of OpenAI embeddings.
    """
    
    def __init__(
        self,
        embedding_service_url: Optional[str] = None,
        use_vietnamese_service: bool = True
    ):
        """
        Initialize the EmbeddingManager.
        
        Args:
            embedding_service_url: URL of the Vietnamese embedding service
            use_vietnamese_service: Whether to use Vietnamese service or fallback to OpenAI
        """
        self.use_vietnamese_service = use_vietnamese_service
        
        if use_vietnamese_service:
            # Use Vietnamese Embedding Service
            service_url = embedding_service_url or getattr(settings, 'VIETNAMESE_EMBEDDING_SERVICE_URL', 'http://localhost:2603')
            self.embeddings = VietnameseEmbeddingService(
                embedding_service_url=service_url,
                normalize=True
            )
        else:
            # Fallback to OpenAI embeddings
            from langchain_openai import OpenAIEmbeddings
            self.embeddings = OpenAIEmbeddings(
                api_key=settings.OPENAI_API_KEY,
                model=settings.EMBEDDING_MODEL
            )
    
    async def get_embeddings(self, texts: List[str]) -> List[List[float]]:
        """
        Get embeddings for a list of texts.
        
        Args:
            texts: List of texts to embed
            
        Returns:
            List of embedding vectors
        """
        return await self.embeddings.aembed_documents(texts)
    
    def health_check(self) -> bool:
        """
        Check if the embedding service is healthy.

        Returns:
            True if service is healthy, False otherwise
        """
        if not self.use_vietnamese_service:
            return True  # Assume OpenAI is always available

        try:
            health_url = f"{self.embeddings.embedding_service_url}/health"
            response = requests.get(health_url, timeout=5)
            return response.status_code == 200
        except (requests.RequestException, ConnectionError, TimeoutError) as e:
            print(f"Health check failed: {e}")
            return False
