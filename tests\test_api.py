#!/usr/bin/env python3
"""
Test script for the updated FAISS-based API
"""

import requests
import json
import sys
import os
from pathlib import Path

# API base URL
BASE_URL = "http://localhost:8000"

def test_api_status():
    """Test if API is running"""
    print("🔍 Testing API status...")
    
    try:
        response = requests.get(f"{BASE_URL}/")
        if response.status_code == 200:
            print("✅ API is running")
            data = response.json()
            print(f"   Response: {data}")
            return True
        else:
            print(f"❌ API returned status {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Cannot connect to API: {e}")
        return False

def test_stats_endpoint():
    """Test the stats endpoint"""
    print("\n📊 Testing /stats endpoint...")
    
    try:
        response = requests.get(f"{BASE_URL}/stats")
        if response.status_code == 200:
            data = response.json()
            print("✅ Stats endpoint working")
            print(f"   Total documents: {data.get('total_documents', 0)}")
            print(f"   Embedding dimension: {data.get('embedding_dimension', 'N/A')}")
            print(f"   Vector store type: {data.get('vector_store_type', 'Unknown')}")
            return True
        else:
            print(f"❌ Stats endpoint returned status {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Error testing stats: {e}")
        return False

def test_search_endpoint():
    """Test the search endpoint"""
    print("\n🔍 Testing /search endpoint...")
    
    try:
        payload = {
            "query": "Đoàn thanh niên",
            "k": 3
        }
        response = requests.post(f"{BASE_URL}/search", json=payload)
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Search endpoint working")
            print(f"   Query: {data.get('query')}")
            print(f"   Results found: {data.get('count', 0)}")
            
            # Show first result if available
            results = data.get('results', [])
            if results:
                first_result = results[0]
                content = first_result.get('content', '')[:100]
                print(f"   First result preview: {content}...")
            
            return True
        else:
            print(f"❌ Search endpoint returned status {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Error testing search: {e}")
        return False

def test_upload_sample_file():
    """Test uploading a sample text file"""
    print("\n📤 Testing file upload...")
    
    # Create a sample PDF-like content (for testing, we'll create a simple text file)
    sample_content = """Đoàn Thanh niên Cộng sản Hồ Chí Minh là tổ chức chính trị - xã hội của thanh niên Việt Nam.
    
Đoàn được thành lập theo chủ nghĩa Mác - Lênin và tư tưởng Hồ Chí Minh, do Đảng Cộng sản Việt Nam lãnh đạo.

Nhiệm vụ chính của Đoàn là tập hợp, đoàn kết, giáo dục thanh niên Việt Nam theo mục tiêu, lý tưởng cách mạng của Đảng.

Độ tuổi gia nhập Đoàn là từ 15 tuổi đến 30 tuổi."""
    
    # Note: This is a mock test since we can't easily create a real PDF
    # In practice, you would use a real PDF file
    print("ℹ️ File upload test requires a real PDF file")
    print("   You can test this manually by:")
    print("   1. Prepare a PDF file")
    print("   2. Use curl or Postman to upload to /files endpoint")
    print("   Example curl command:")
    print(f'   curl -X POST "{BASE_URL}/files" \\')
    print('     -F "file=@your_file.pdf" \\')
    print('     -F "link=https://example.com" \\')
    print('     -F "description=Test file" \\')
    print('     -F "tags=test,sample"')
    
    return True

def test_web_content():
    """Test web content processing"""
    print("\n🌐 Testing web content processing...")
    
    try:
        payload = {
            "url": "https://example.com",
            "type_document": "Website"
        }
        
        # Note: This will likely fail without proper web scraping setup
        print("ℹ️ Web content test requires proper HTML processing")
        print("   This test might fail if web scraping is not properly configured")
        print(f"   You can test manually with: POST {BASE_URL}/web-content")
        print(f"   Payload: {json.dumps(payload, indent=2)}")
        
        return True
    except Exception as e:
        print(f"❌ Error testing web content: {e}")
        return False

def test_chat_endpoint():
    """Test the chat endpoint"""
    print("\n💬 Testing chat endpoint...")
    
    try:
        payload = {
            "text": "Độ tuổi gia nhập đoàn là bao nhiêu?",
            "session": "test_session_123"
        }
        
        response = requests.post(f"{BASE_URL}/docs-rag-agent", json=payload)
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Chat endpoint working")
            print(f"   Success: {data.get('success')}")
            output = data.get('output', '')[:200]
            print(f"   Response preview: {output}...")
            return True
        else:
            print(f"❌ Chat endpoint returned status {response.status_code}")
            print(f"   Response: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Error testing chat: {e}")
        return False

def run_all_tests():
    """Run all tests"""
    print("🚀 Starting API tests for FAISS-based chatbot")
    print("=" * 60)
    
    tests = [
        ("API Status", test_api_status),
        ("Stats Endpoint", test_stats_endpoint),
        ("Search Endpoint", test_search_endpoint),
        ("File Upload", test_upload_sample_file),
        ("Web Content", test_web_content),
        ("Chat Endpoint", test_chat_endpoint)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test '{test_name}' failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 TEST SUMMARY")
    print("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\nTotal: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 All tests passed!")
    else:
        print("⚠️ Some tests failed. Check the output above for details.")
    
    return passed == len(results)

def print_manual_test_commands():
    """Print manual test commands"""
    print("\n📝 MANUAL TESTING COMMANDS")
    print("=" * 60)
    
    print("\n1. Test file upload:")
    print(f'curl -X POST "{BASE_URL}/files" \\')
    print('  -F "file=@path/to/your/file.pdf" \\')
    print('  -F "link=https://example.com" \\')
    print('  -F "description=Test document" \\')
    print('  -F "tags=test,sample"')
    
    print("\n2. Test web content:")
    print(f'curl -X POST "{BASE_URL}/web-content" \\')
    print('  -F "url=https://example.com" \\')
    print('  -F "type_document=Website"')
    
    print("\n3. Test search:")
    print(f'curl -X POST "{BASE_URL}/search" \\')
    print('  -H "Content-Type: application/json" \\')
    print('  -d \'{"query": "Đoàn thanh niên", "k": 3}\'')
    
    print("\n4. Test chat:")
    print(f'curl -X POST "{BASE_URL}/docs-rag-agent" \\')
    print('  -H "Content-Type: application/json" \\')
    print('  -d \'{"text": "Độ tuổi gia nhập đoàn là bao nhiêu?", "session": "test123"}\'')
    
    print("\n5. Get stats:")
    print(f'curl -X GET "{BASE_URL}/stats"')

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "--manual":
        print_manual_test_commands()
    else:
        success = run_all_tests()
        print_manual_test_commands()
        
        if success:
            sys.exit(0)
        else:
            sys.exit(1)
