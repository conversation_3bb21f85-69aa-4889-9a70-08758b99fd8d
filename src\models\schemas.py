from pydantic import BaseModel, Field
from typing import List, Optional

class Message(BaseModel):
    text: str
    session: str

class ChatHistory(BaseModel):
    messages: List[Message]

class ChatRequest(BaseModel):
    input: str
    history: Optional[ChatHistory]

class DocumentReference(BaseModel):
    filename: str
    link: Optional[str] = None


class ChatResponse(BaseModel):
    success: bool = True
    output: str
    intermediate_steps: List[str]
    references: List[DocumentReference] = []


class Document(BaseModel):
    content: str
    metadata: Optional[dict]